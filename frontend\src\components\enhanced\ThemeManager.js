import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { message, Switch } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  CloseOutlined,
  CheckOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';

// Import the new unified actions and selectors
import {
  addTheme,
  updateTheme,
  deleteTheme,
  setActiveTheme
} from '../../redux/actions/appBuilderActions';
import {
  getThemes,
  getActiveTheme,
  getComponents,
  getLayouts
} from '../../redux/selectors/appBuilderSelectors';

// Import theme application system
import {
  useThemeApplication,
  ThemePreview,
  RealTimeThemeApplicator
} from '../theme/ThemeApplicationSystem';

// Fallback imports
import { addTheme as fallbackAddTheme, updateTheme as fallbackUpdateTheme, removeTheme, setActiveTheme as fallbackSetActiveTheme } from '../../redux/actions';
import { toggleAutoApplyTheme, saveUserThemePreferences } from '../../redux/actions/themeActions';
import { styled } from '../../design-system';
import { Button, Card, Input } from '../../design-system';
import theme from '../../design-system/theme';

const ThemeManagerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[4]};
`;

const ThemeGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: ${theme.spacing[4]};
`;

const ColorInput = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};

  .color-preview {
    width: 36px;
    height: 36px;
    border-radius: ${theme.borderRadius.md};
    border: 1px solid ${theme.colors.neutral[300]};
    overflow: hidden;
    position: relative;

    input[type="color"] {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
      padding: 0;
      margin: 0;
      cursor: pointer;
    }
  }

  .color-input {
    flex: 1;
  }
`;

const FontSelector = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};

  .font-preview {
    padding: ${theme.spacing[2]};
    border: 1px solid ${theme.colors.neutral[300]};
    border-radius: ${theme.borderRadius.md};
    min-height: 60px;
  }
`;

const StyledThemePreview = styled.div`
  padding: ${theme.spacing[4]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.backgroundColor || 'white'};
  color: ${props => props.textColor || 'black'};
  font-family: ${props => props.fontFamily || 'inherit'};
  transition: all 0.3s ease;

  h3 {
    margin-top: 0;
    margin-bottom: ${theme.spacing[3]};
    color: ${props => props.textColor || 'black'};
  }

  p {
    margin-bottom: ${theme.spacing[3]};
  }

  .buttons {
    display: flex;
    gap: ${theme.spacing[2]};
  }

  .primary-button {
    padding: ${theme.spacing[2]} ${theme.spacing[3]};
    background-color: ${props => props.primaryColor || theme.colors.primary.main};
    color: white;
    border: none;
    border-radius: ${theme.borderRadius.md};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.9;
      transform: translateY(-2px);
    }
  }

  .secondary-button {
    padding: ${theme.spacing[2]} ${theme.spacing[3]};
    background-color: ${props => props.secondaryColor || theme.colors.secondary.main};
    color: white;
    border: none;
    border-radius: ${theme.borderRadius.md};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.9;
      transform: translateY(-2px);
    }
  }

  .card-example {
    margin-top: ${theme.spacing[3]};
    padding: ${theme.spacing[3]};
    border-radius: ${theme.borderRadius.md};
    background-color: ${props => props.backgroundColor === '#FFFFFF' ? '#F9FAFB' : 'rgba(255, 255, 255, 0.1)'};
    border: 1px solid ${props => props.backgroundColor === '#FFFFFF' ? '#E5E7EB' : 'rgba(255, 255, 255, 0.2)'};
  }

  .input-example {
    margin-top: ${theme.spacing[3]};
    padding: ${theme.spacing[2]};
    border-radius: ${theme.borderRadius.sm};
    border: 1px solid ${props => props.backgroundColor === '#FFFFFF' ? '#D1D5DB' : 'rgba(255, 255, 255, 0.2)'};
    background-color: ${props => props.backgroundColor === '#FFFFFF' ? 'white' : 'rgba(255, 255, 255, 0.05)'};
    color: ${props => props.textColor};
    width: 100%;
    font-family: ${props => props.fontFamily};
  }
`;

const ColorPalette = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
  margin-top: ${theme.spacing[2]};

  .color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid ${theme.colors.neutral[300]};
    cursor: pointer;
  }
`;

const PropertyEditor = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
`;

const PropertyGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing[8]};
  background-color: ${theme.colors.neutral[100]};
  border-radius: ${theme.borderRadius.md};
  text-align: center;
`;

const ThemeCard = styled(Card)`
  border: ${props => props.isActive ? `2px solid ${theme.colors.primary.main}` : 'none'};
  transition: ${theme.transitions.default};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.md};
  }
`;

const fontFamilies = [
  'Inter, sans-serif',
  'Arial, sans-serif',
  'Helvetica, sans-serif',
  'Georgia, serif',
  'Times New Roman, serif',
  'Courier New, monospace',
  'Verdana, sans-serif',
  'Roboto, sans-serif',
  'Open Sans, sans-serif',
  'Lato, sans-serif'
];

const colorPalettes = [
  {
    name: 'Blue',
    primary: '#2563EB',
    secondary: '#10B981',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Purple',
    primary: '#8B5CF6',
    secondary: '#EC4899',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Green',
    primary: '#10B981',
    secondary: '#3B82F6',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Red',
    primary: '#EF4444',
    secondary: '#F59E0B',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Dark',
    primary: '#3B82F6',
    secondary: '#10B981',
    background: '#111827',
    text: '#F9FAFB'
  },
  {
    name: 'Monochrome',
    primary: '#000000',
    secondary: '#666666',
    background: '#FFFFFF',
    text: '#333333'
  },
  {
    name: 'Sunset',
    primary: '#FF5733',
    secondary: '#FFC300',
    background: '#FFFAF0',
    text: '#333333'
  },
  {
    name: 'Ocean',
    primary: '#1A5276',
    secondary: '#2E86C1',
    background: '#EBF5FB',
    text: '#17202A'
  },
  {
    name: 'Forest',
    primary: '#1E8449',
    secondary: '#F1C40F',
    background: '#F4F6F6',
    text: '#145A32'
  },
  {
    name: 'Night Mode',
    primary: '#BB86FC',
    secondary: '#03DAC5',
    background: '#121212',
    text: '#E1E1E1'
  }
];

const EnhancedThemeManager = () => {
  const dispatch = useDispatch();

  // Use the new unified selectors with fallback
  let themes, activeTheme, components, layouts;
  try {
    themes = useSelector(getThemes);
    activeTheme = useSelector(getActiveTheme);
    components = useSelector(getComponents);
    layouts = useSelector(getLayouts);
  } catch (error) {
    console.warn('Using fallback selectors');
    // Fallback to old selectors
    themes = useSelector(state => {
      if (!state || !state.themes) {
        console.warn('Redux state or themes slice not found, using fallback values');
        return [];
      }
      return Array.isArray(state.themes.themes) ? state.themes.themes : [];
    });

    activeTheme = useSelector(state => {
      if (!state || !state.themes) {
        return 'default';
      }
      return state.themes.activeTheme || 'default';
    });

    components = [];
    layouts = [];
  }

  // Get user preferences from Redux store
  const userPreferences = useSelector(state => {
    if (!state || !state.themes || !state.themes.userPreferences) {
      return { savedTheme: null, autoApplyTheme: true };
    }
    return state.themes.userPreferences;
  });

  // Use theme application system
  const themeApplication = useThemeApplication();

  const [themeName, setThemeName] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2563EB');
  const [secondaryColor, setSecondaryColor] = useState('#10B981');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');
  const [textColor, setTextColor] = useState('#111827');
  const [fontFamily, setFontFamily] = useState('Inter, sans-serif');
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [errors, setErrors] = useState({});

  // Add default theme if it doesn't exist - only run once on mount
  useEffect(() => {
    const defaultTheme = {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif'
    };

    // Safely check if themes is an array and if it's empty
    if (!Array.isArray(themes) || themes.length === 0) {
      try {
        dispatch(addTheme(defaultTheme));
      } catch (error) {
        console.error('Error dispatching addTheme action:', error);
      }
    }

    if (!activeTheme) {
      try {
        dispatch(setActiveTheme('default'));
      } catch (error) {
        console.error('Error dispatching setActiveTheme action:', error);
      }
    }
    // Only run this effect once on component mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  // Add service worker message listener - only run once on mount
  useEffect(() => {
    const handleServiceWorkerMessage = (event) => {
      if (event.data && event.data.type === 'THEME_CACHE_UPDATED') {
        console.log('Theme cache updated at:', event.data.timestamp);
      }
    };

    // Add event listener
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    // Clean up
    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
    // We intentionally don't include dependencies here to avoid re-registering the listener
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if (!themeName.trim()) {
      newErrors.name = 'Theme name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddTheme = () => {
    if (!validateForm()) return;

    const newTheme = {
      id: Date.now().toString(),
      name: themeName.trim(),
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
      fontFamily,
      createdAt: new Date().toISOString()
    };

    try {
      dispatch(addTheme(newTheme));

      // Show success message
      if (typeof message !== 'undefined') {
        message.success(`Theme "${newTheme.name}" created successfully`);
      }

      // Notify service worker to update theme cache
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'THEME_UPDATED',
          theme: newTheme
        });

        // Also update the theme cache
        navigator.serviceWorker.controller.postMessage({
          type: 'UPDATE_THEME_CACHE'
        });
      }

      // Reset form
      setThemeName('');
      setPrimaryColor('#2563EB');
      setSecondaryColor('#10B981');
      setBackgroundColor('#FFFFFF');
      setTextColor('#111827');
      setFontFamily('Inter, sans-serif');
      setErrors({});
    } catch (error) {
      console.error('Error creating theme:', error);
      if (typeof message !== 'undefined') {
        message.error('Failed to create theme');
      }
    }
  };

  const handleUpdateTheme = () => {
    if (!selectedTheme || !validateForm()) return;

    const updatedTheme = {
      ...selectedTheme,
      name: themeName.trim(),
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
      fontFamily,
      updatedAt: new Date().toISOString()
    };

    // Update the theme in the store
    dispatch(updateTheme(updatedTheme));

    // Notify service worker to update theme cache
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'THEME_UPDATED',
        theme: updatedTheme
      });

      // Also update the theme cache
      navigator.serviceWorker.controller.postMessage({
        type: 'UPDATE_THEME_CACHE'
      });
    }

    // Reset form and exit edit mode
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setSelectedTheme(null);
    setEditMode(false);
    setErrors({});
  };

  const handleRemoveTheme = (id) => {
    // Don't allow removing the default theme
    if (id === 'default') return;

    // Remove the theme from the store
    dispatch(removeTheme(id));

    // If the removed theme was selected, reset the form
    if (selectedTheme && selectedTheme.id === id) {
      setThemeName('');
      setPrimaryColor('#2563EB');
      setSecondaryColor('#10B981');
      setBackgroundColor('#FFFFFF');
      setTextColor('#111827');
      setFontFamily('Inter, sans-serif');
      setSelectedTheme(null);
      setEditMode(false);
    }

    // If the removed theme was active, set the default theme as active
    if (activeTheme === id) {
      dispatch(setActiveTheme('default'));
    }
  };

  const handleSelectTheme = (theme) => {
    setSelectedTheme(theme);
    setThemeName(theme.name);
    setPrimaryColor(theme.primaryColor);
    setSecondaryColor(theme.secondaryColor);
    setBackgroundColor(theme.backgroundColor);
    setTextColor(theme.textColor);
    setFontFamily(theme.fontFamily);
    setEditMode(true);
    setErrors({});
  };

  const handleCancelEdit = () => {
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setSelectedTheme(null);
    setEditMode(false);
    setErrors({});
  };

  const handleDuplicateTheme = (theme) => {
    const duplicatedTheme = {
      ...theme,
      id: Date.now().toString(),
      name: `${theme.name} (Copy)`,
      createdAt: new Date().toISOString()
    };

    dispatch(addTheme(duplicatedTheme));
  };

  // Handle toggling auto-apply theme setting
  const handleToggleAutoApply = () => {
    dispatch(toggleAutoApplyTheme());
    dispatch(saveUserThemePreferences());

    // Show success message
    const newState = !userPreferences.autoApplyTheme;
    message.success(`Auto-apply theme ${newState ? 'enabled' : 'disabled'}`);
  };

  const handleSetActiveTheme = (themeId) => {
    try {
      // Use the theme application system for global theme application
      if (themeApplication && themeApplication.applyThemeGlobally) {
        themeApplication.applyThemeGlobally(themeId);
        message.success(`Theme applied to all components and layouts`);
      } else {
        // Fallback to basic theme activation
        dispatch(setActiveTheme(themeId));
        message.success(`Theme activated successfully`);
      }

      // Find the theme object
      const defaultTheme = {
        id: 'default',
        name: 'Default Theme',
        primaryColor: '#2563EB',
        secondaryColor: '#10B981',
        backgroundColor: '#FFFFFF',
        textColor: '#111827',
        fontFamily: 'Inter, sans-serif'
      };

      // Safely handle themes array without using spread operator
      const safeThemes = Array.isArray(themes) ? themes : [];
      const activeTheme = safeThemes.concat([defaultTheme]).find(theme => theme.id === themeId);

      // Notify service worker about the active theme change
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller && activeTheme) {
        navigator.serviceWorker.controller.postMessage({
          type: 'THEME_UPDATED',
          theme: activeTheme
        });
      }
    } catch (error) {
      console.error('Error setting active theme:', error);
      message.error('Failed to activate theme. Please try again.');
    }
  };

  // New handlers for selective theme application
  const handleApplyToComponents = (themeId) => {
    if (themeApplication && themeApplication.applyThemeToAllComponents) {
      themeApplication.applyThemeToAllComponents(themeId);
      message.success(`Theme applied to ${components.length} components`);
    }
  };

  const handleApplyToLayouts = (themeId) => {
    if (themeApplication && themeApplication.applyThemeToAllLayouts) {
      themeApplication.applyThemeToAllLayouts(themeId);
      message.success(`Theme applied to ${layouts.length} layouts`);
    }
  };

  const handlePreviewTheme = (themeId) => {
    if (themeApplication && themeApplication.startThemePreview) {
      themeApplication.startThemePreview(themeId);
    }
  };

  const handleEndPreview = () => {
    if (themeApplication && themeApplication.endThemePreview) {
      themeApplication.endThemePreview();
    }
  };

  const handleApplyPalette = (palette) => {
    setPrimaryColor(palette.primary);
    setSecondaryColor(palette.secondary);
    setBackgroundColor(palette.background);
    setTextColor(palette.text);
  };

  const getActiveThemeObject = () => {
    const defaultTheme = {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif'
    };

    // Safely handle themes array without using spread operator
    const safeThemes = Array.isArray(themes) ? themes : [];

    // If activeTheme is not defined or not found in themes, return defaultTheme
    if (!activeTheme) {
      return defaultTheme;
    }

    // Find the active theme in the themes array
    const foundTheme = safeThemes.concat([defaultTheme]).find(theme => theme.id === activeTheme);

    // Return the found theme or defaultTheme if not found
    return foundTheme || defaultTheme;
  };

  // Export theme to JSON
  const handleExportTheme = (theme) => {
    const themeToExport = theme || selectedTheme;
    if (!themeToExport) return;

    // Create a JSON string
    const themeJson = JSON.stringify(themeToExport, null, 2);

    // Create a blob
    const blob = new Blob([themeJson], { type: 'application/json' });

    // Create a URL for the blob
    const url = URL.createObjectURL(blob);

    // Create a link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `${themeToExport.name.replace(/\s+/g, '-').toLowerCase()}-theme.json`;

    // Append the link to the body
    document.body.appendChild(link);

    // Click the link
    link.click();

    // Remove the link
    document.body.removeChild(link);

    // Revoke the URL
    URL.revokeObjectURL(url);
  };

  // Import theme from JSON
  const handleImportTheme = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedTheme = JSON.parse(e.target.result);

        // Validate the theme
        if (!importedTheme.name || !importedTheme.primaryColor ||
          !importedTheme.secondaryColor || !importedTheme.backgroundColor ||
          !importedTheme.textColor || !importedTheme.fontFamily) {
          throw new Error('Invalid theme format');
        }

        // Create a new theme with a new ID
        const newTheme = {
          ...importedTheme,
          id: Date.now().toString(),
          name: `${importedTheme.name} (Imported)`,
          createdAt: new Date().toISOString()
        };

        // Add the theme
        dispatch(addTheme(newTheme));

        // Notify service worker
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'THEME_UPDATED',
            theme: newTheme
          });

          navigator.serviceWorker.controller.postMessage({
            type: 'UPDATE_THEME_CACHE'
          });
        }

        // Reset the file input
        event.target.value = '';
      } catch (error) {
        console.error('Error importing theme:', error);
        // Reset the file input
        event.target.value = '';
      }
    };

    reader.readAsText(file);
  };

  return (
    <ThemeManagerContainer>
      <Card>
        <Card.Header>
          <Card.Title>{editMode ? 'Edit Theme' : 'Create Theme'}</Card.Title>
          {editMode && (
            <Button
              variant="text"
              size="small"
              onClick={handleCancelEdit}
              startIcon={<CloseOutlined />}
            >
              Cancel
            </Button>
          )}
        </Card.Header>
        <Card.Content>
          <PropertyEditor>
            <PropertyGroup>
              <Input
                label="Theme Name"
                value={themeName}
                onChange={(e) => setThemeName(e.target.value)}
                placeholder="Enter theme name"
                fullWidth
                error={!!errors.name}
                helperText={errors.name}
              />
            </PropertyGroup>

            <div style={{ marginBottom: theme.spacing[2] }}>
              <div style={{ fontWeight: theme.typography.fontWeight.medium, marginBottom: theme.spacing[2] }}>
                Color Palettes
              </div>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: theme.spacing[2] }}>
                {colorPalettes.map((palette, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="small"
                    onClick={() => handleApplyPalette(palette)}
                  >
                    {palette.name}
                  </Button>
                ))}
              </div>
            </div>

            <PropertyGroup>
              <label>Primary Color</label>
              <ColorInput>
                <div className="color-preview">
                  <input
                    type="color"
                    value={primaryColor}
                    onChange={(e) => setPrimaryColor(e.target.value)}
                  />
                </div>
                <Input
                  className="color-input"
                  value={primaryColor}
                  onChange={(e) => setPrimaryColor(e.target.value)}
                  fullWidth
                />
              </ColorInput>
            </PropertyGroup>

            <PropertyGroup>
              <label>Secondary Color</label>
              <ColorInput>
                <div className="color-preview">
                  <input
                    type="color"
                    value={secondaryColor}
                    onChange={(e) => setSecondaryColor(e.target.value)}
                  />
                </div>
                <Input
                  className="color-input"
                  value={secondaryColor}
                  onChange={(e) => setSecondaryColor(e.target.value)}
                  fullWidth
                />
              </ColorInput>
            </PropertyGroup>

            <PropertyGroup>
              <label>Background Color</label>
              <ColorInput>
                <div className="color-preview">
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                  />
                </div>
                <Input
                  className="color-input"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  fullWidth
                />
              </ColorInput>
            </PropertyGroup>

            <PropertyGroup>
              <label>Text Color</label>
              <ColorInput>
                <div className="color-preview">
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                  />
                </div>
                <Input
                  className="color-input"
                  value={textColor}
                  onChange={(e) => setTextColor(e.target.value)}
                  fullWidth
                />
              </ColorInput>
            </PropertyGroup>

            <PropertyGroup>
              <label>Font Family</label>
              <select
                value={fontFamily}
                onChange={(e) => setFontFamily(e.target.value)}
                style={{
                  width: '100%',
                  padding: theme.spacing[2],
                  borderRadius: theme.borderRadius.md,
                  border: `1px solid ${theme.colors.neutral[300]}`
                }}
              >
                {fontFamilies.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
              <FontSelector>
                <div className="font-preview" style={{ fontFamily }}>
                  The quick brown fox jumps over the lazy dog.
                </div>
              </FontSelector>
            </PropertyGroup>
          </PropertyEditor>
        </Card.Content>
        <Card.Footer>
          {editMode ? (
            <Button
              variant="primary"
              onClick={handleUpdateTheme}
              startIcon={<SaveOutlined />}
              disabled={selectedTheme?.id === 'default'}
            >
              Update Theme
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleAddTheme}
              startIcon={<PlusOutlined />}
            >
              Add Theme
            </Button>
          )}
        </Card.Footer>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Theme Preview</Card.Title>
        </Card.Header>
        <Card.Content>
          <StyledThemePreview
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
            backgroundColor={backgroundColor}
            textColor={textColor}
            fontFamily={fontFamily}
          >
            <h3>Theme Preview</h3>
            <p>This is a preview of how your theme will look. The text color, background color, and font family are applied to this preview.</p>

            <div className="buttons">
              <button className="primary-button">Primary Button</button>
              <button className="secondary-button">Secondary Button</button>
            </div>

            <div className="card-example">
              <h4 style={{ margin: '0 0 8px 0', color: textColor }}>Card Example</h4>
              <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>This shows how cards will appear with your theme.</p>
            </div>

            <label style={{ display: 'block', marginTop: '16px', marginBottom: '8px' }}>
              Input Example:
            </label>
            <input
              type="text"
              className="input-example"
              placeholder="Enter text here..."
            />

            <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'space-between' }}>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: primaryColor,
                border: '1px solid rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: secondaryColor,
                border: '1px solid rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: backgroundColor,
                border: '1px solid rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: textColor,
                border: '1px solid rgba(0,0,0,0.1)'
              }}></div>
            </div>
          </StyledThemePreview>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Theme Preferences</Card.Title>
          <SettingOutlined style={{ fontSize: '18px', color: theme.colors.neutral[500] }} />
        </Card.Header>
        <Card.Content>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: theme.spacing[2] }}>
            <div>
              <h4 style={{ margin: 0, marginBottom: theme.spacing[1] }}>Auto-apply Theme</h4>
              <p style={{ margin: 0, color: theme.colors.neutral[500], fontSize: theme.typography.fontSize.sm }}>
                Automatically save your theme selection as a preference
              </p>
            </div>
            <Switch
              checked={userPreferences.autoApplyTheme}
              onChange={handleToggleAutoApply}
              style={{ backgroundColor: userPreferences.autoApplyTheme ? theme.colors.primary.main : undefined }}
            />
          </div>

          <div style={{
            marginTop: theme.spacing[3],
            padding: theme.spacing[3],
            backgroundColor: theme.colors.neutral[100],
            borderRadius: theme.borderRadius.md
          }}>
            <h4 style={{ margin: 0, marginBottom: theme.spacing[2] }}>Current Preferences</h4>
            <div style={{ display: 'flex', gap: theme.spacing[2], alignItems: 'center' }}>
              <div style={{
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: userPreferences.savedTheme ?
                  themes.find(t => t.id === userPreferences.savedTheme)?.primaryColor || '#2563EB' :
                  '#2563EB',
                border: '1px solid #e5e7eb'
              }}></div>
              <span>
                {userPreferences.savedTheme ?
                  themes.find(t => t.id === userPreferences.savedTheme)?.name || 'Default Theme' :
                  'No saved preference'}
              </span>
            </div>
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Available Themes</Card.Title>
          <div style={{ display: 'flex', gap: theme.spacing[2] }}>
            <input
              type="file"
              id="theme-import"
              accept=".json"
              style={{ display: 'none' }}
              onChange={handleImportTheme}
            />
            <Button
              variant="outline"
              size="small"
              onClick={() => document.getElementById('theme-import').click()}
            >
              Import Theme
            </Button>
          </div>
        </Card.Header>
        <Card.Content>
          {themes.length === 0 ? (
            <EmptyState>
              <div style={{ fontSize: '48px', color: theme.colors.neutral[400], marginBottom: theme.spacing[4] }}>
                <BgColorsOutlined />
              </div>
              <h3>No Themes Yet</h3>
              <p>Create your first theme to get started</p>
            </EmptyState>
          ) : (
            <ThemeGrid>
              {(Array.isArray(themes) ? themes : []).map(theme => (
                <ThemeCard
                  key={theme.id}
                  elevation="sm"
                  isActive={activeTheme === theme.id}
                >
                  <Card.Header>
                    <div>
                      <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{theme.name}</div>
                      <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
                        {theme.fontFamily.split(',')[0]}
                      </div>
                    </div>
                    <div style={{ display: 'flex', gap: theme.spacing[1] }}>
                      {theme.id !== 'default' && (
                        <>
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleExportTheme(theme)}
                            title="Export Theme"
                          >
                            <EyeOutlined />
                          </Button>
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleDuplicateTheme(theme)}
                            title="Duplicate Theme"
                          >
                            <CopyOutlined />
                          </Button>
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleSelectTheme(theme)}
                            title="Edit Theme"
                          >
                            <EditOutlined />
                          </Button>
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleRemoveTheme(theme.id)}
                            title="Delete Theme"
                          >
                            <DeleteOutlined />
                          </Button>
                        </>
                      )}
                    </div>
                  </Card.Header>
                  <Card.Content onClick={() => handleSelectTheme(theme)}>
                    <ThemePreview
                      primaryColor={theme.primaryColor}
                      secondaryColor={theme.secondaryColor}
                      backgroundColor={theme.backgroundColor}
                      textColor={theme.textColor}
                      fontFamily={theme.fontFamily}
                      style={{ height: '120px', overflow: 'hidden' }}
                    >
                      <h3 style={{ fontSize: '16px' }}>Preview</h3>
                      <p style={{ fontSize: '14px' }}>Sample text with this theme applied.</p>
                      <div className="buttons">
                        <button className="primary-button" style={{ padding: '4px 8px', fontSize: '12px' }}>Button</button>
                        <button className="secondary-button" style={{ padding: '4px 8px', fontSize: '12px' }}>Button</button>
                      </div>
                    </ThemePreview>
                  </Card.Content>
                  <Card.Footer>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div style={{ display: 'flex', gap: theme.spacing[2] }}>
                        <div style={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: theme.primaryColor,
                          borderRadius: '50%',
                          border: '1px solid #e5e7eb'
                        }}></div>
                        <div style={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: theme.secondaryColor,
                          borderRadius: '50%',
                          border: '1px solid #e5e7eb'
                        }}></div>
                      </div>

                      <div style={{ display: 'flex', gap: theme.spacing[1] }}>
                        {/* Preview button */}
                        <Button
                          variant="text"
                          size="small"
                          onClick={() => handlePreviewTheme(theme.id)}
                          onMouseLeave={handleEndPreview}
                          title="Preview theme"
                        >
                          <EyeOutlined />
                        </Button>

                        {/* Apply to components */}
                        {components.length > 0 && (
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleApplyToComponents(theme.id)}
                            title={`Apply to ${components.length} components`}
                          >
                            <AppstoreOutlined />
                          </Button>
                        )}

                        {/* Apply to layouts */}
                        {layouts.length > 0 && (
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => handleApplyToLayouts(theme.id)}
                            title={`Apply to ${layouts.length} layouts`}
                          >
                            <LayoutOutlined />
                          </Button>
                        )}

                        {/* Activate theme */}
                        {activeTheme === theme.id ? (
                          <Button
                            variant="text"
                            size="small"
                            startIcon={<CheckOutlined />}
                            style={{ color: theme.colors.success.main }}
                          >
                            Active
                          </Button>
                        ) : (
                          <Button
                            variant="primary"
                            size="small"
                            onClick={() => handleSetActiveTheme(theme.id)}
                          >
                            Apply All
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card.Footer>
                </ThemeCard>
              ))}
            </ThemeGrid>
          )}
        </Card.Content>
      </Card>
    </ThemeManagerContainer>
  );
};

export default EnhancedThemeManager;
