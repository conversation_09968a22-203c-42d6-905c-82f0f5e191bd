import { useState, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectAppBuilderState } from '../redux/selectors';

/**
 * Custom hook for AI-specific undo/redo functionality
 * Tracks changes made by AI suggestions and allows reverting them
 */
export const useAIUndoRedo = (options = {}) => {
  const {
    maxHistorySize = 50,
    enableGrouping = true,
    groupTimeout = 1000 // Group actions within 1 second
  } = options;

  const dispatch = useDispatch();

  // Get current app state using memoized selector
  const currentState = useSelector(selectAppBuilderState);

  // History management
  const [history, setHistory] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [isUndoing, setIsUndoing] = useState(false);
  const [isRedoing, setIsRedoing] = useState(false);

  // Refs for grouping
  const lastActionTimeRef = useRef(0);
  const pendingGroupRef = useRef(null);

  /**
   * Save current state to history
   */
  const saveState = useCallback((action, metadata = {}) => {
    const timestamp = Date.now();
    const stateSnapshot = {
      id: `ai_action_${timestamp}`,
      timestamp,
      action,
      metadata: {
        ...metadata,
        isAIGenerated: true
      },
      state: {
        components: JSON.parse(JSON.stringify(currentState.components)),
        layouts: JSON.parse(JSON.stringify(currentState.layouts)),
        styles: JSON.parse(JSON.stringify(currentState.styles))
      }
    };

    setHistory(prevHistory => {
      let newHistory = [...prevHistory];

      // Remove any history after current index (when undoing and then making new changes)
      if (currentIndex < newHistory.length - 1) {
        newHistory = newHistory.slice(0, currentIndex + 1);
      }

      // Handle grouping
      if (enableGrouping && pendingGroupRef.current) {
        const timeDiff = timestamp - lastActionTimeRef.current;

        if (timeDiff < groupTimeout &&
          pendingGroupRef.current.action.type === action.type) {
          // Update the pending group instead of creating new entry
          pendingGroupRef.current = stateSnapshot;
          newHistory[newHistory.length - 1] = stateSnapshot;
          lastActionTimeRef.current = timestamp;
          return newHistory;
        }
      }

      // Add new state
      newHistory.push(stateSnapshot);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory = newHistory.slice(-maxHistorySize);
      }

      // Update grouping refs
      pendingGroupRef.current = stateSnapshot;
      lastActionTimeRef.current = timestamp;

      return newHistory;
    });

    setCurrentIndex(prevIndex => {
      const newIndex = Math.min(prevIndex + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentState, currentIndex, maxHistorySize, enableGrouping, groupTimeout]);

  /**
   * Undo last AI action
   */
  const undo = useCallback(() => {
    if (currentIndex < 0 || history.length === 0) {
      return false;
    }

    setIsUndoing(true);

    try {
      const previousState = history[currentIndex];

      // Restore previous state
      if (previousState) {
        // Dispatch actions to restore state
        // This would need to be integrated with your specific Redux actions
        console.log('Undoing AI action:', previousState.action);

        // Example: dispatch restore actions
        // dispatch(restoreComponents(previousState.state.components));
        // dispatch(restoreLayouts(previousState.state.layouts));
        // dispatch(restoreStyles(previousState.state.styles));

        setCurrentIndex(prevIndex => prevIndex - 1);
        return true;
      }
    } catch (error) {
      console.error('Error undoing AI action:', error);
    } finally {
      setIsUndoing(false);
    }

    return false;
  }, [currentIndex, history, dispatch]);

  /**
   * Redo next AI action
   */
  const redo = useCallback(() => {
    if (currentIndex >= history.length - 1) {
      return false;
    }

    setIsRedoing(true);

    try {
      const nextState = history[currentIndex + 1];

      if (nextState) {
        // Restore next state
        console.log('Redoing AI action:', nextState.action);

        // Example: dispatch restore actions
        // dispatch(restoreComponents(nextState.state.components));
        // dispatch(restoreLayouts(nextState.state.layouts));
        // dispatch(restoreStyles(nextState.state.styles));

        setCurrentIndex(prevIndex => prevIndex + 1);
        return true;
      }
    } catch (error) {
      console.error('Error redoing AI action:', error);
    } finally {
      setIsRedoing(false);
    }

    return false;
  }, [currentIndex, history, dispatch]);

  /**
   * Clear all AI history
   */
  const clearHistory = useCallback(() => {
    setHistory([]);
    setCurrentIndex(-1);
    pendingGroupRef.current = null;
    lastActionTimeRef.current = 0;
  }, []);

  /**
   * Get specific history entry
   */
  const getHistoryEntry = useCallback((index) => {
    if (index >= 0 && index < history.length) {
      return history[index];
    }
    return null;
  }, [history]);

  /**
   * Jump to specific history state
   */
  const jumpToState = useCallback((index) => {
    if (index < 0 || index >= history.length) {
      return false;
    }

    try {
      const targetState = history[index];

      if (targetState) {
        console.log('Jumping to AI state:', targetState.action);

        // Restore target state
        // dispatch(restoreComponents(targetState.state.components));
        // dispatch(restoreLayouts(targetState.state.layouts));
        // dispatch(restoreStyles(targetState.state.styles));

        setCurrentIndex(index);
        return true;
      }
    } catch (error) {
      console.error('Error jumping to AI state:', error);
    }

    return false;
  }, [history, dispatch]);

  /**
   * Track AI layout application
   */
  const trackLayoutApplication = useCallback((layoutSuggestion, appliedComponents) => {
    saveState(
      {
        type: 'APPLY_AI_LAYOUT',
        layoutId: layoutSuggestion.id,
        layoutName: layoutSuggestion.name
      },
      {
        suggestion: layoutSuggestion,
        appliedComponents,
        description: `Applied AI layout: ${layoutSuggestion.name}`
      }
    );
  }, [saveState]);

  /**
   * Track AI component combination application
   */
  const trackComponentCombination = useCallback((combinationSuggestion, addedComponents) => {
    saveState(
      {
        type: 'APPLY_AI_COMBINATION',
        combinationId: combinationSuggestion.id,
        combinationName: combinationSuggestion.name
      },
      {
        suggestion: combinationSuggestion,
        addedComponents,
        description: `Applied AI combination: ${combinationSuggestion.name}`
      }
    );
  }, [saveState]);

  /**
   * Track bulk AI changes
   */
  const trackBulkAIChanges = useCallback((changes, description) => {
    saveState(
      {
        type: 'BULK_AI_CHANGES',
        changeCount: changes.length
      },
      {
        changes,
        description: description || `Applied ${changes.length} AI changes`
      }
    );
  }, [saveState]);

  // Computed values
  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < history.length - 1;
  const historySize = history.length;
  const currentPosition = currentIndex + 1;

  return {
    // State
    history,
    currentIndex,
    isUndoing,
    isRedoing,

    // Actions
    undo,
    redo,
    clearHistory,
    jumpToState,
    getHistoryEntry,

    // AI-specific tracking
    trackLayoutApplication,
    trackComponentCombination,
    trackBulkAIChanges,

    // Computed values
    canUndo,
    canRedo,
    historySize,
    currentPosition,

    // Utilities
    getUndoDescription: () => {
      const entry = getHistoryEntry(currentIndex);
      return entry?.metadata?.description || 'Undo AI action';
    },

    getRedoDescription: () => {
      const entry = getHistoryEntry(currentIndex + 1);
      return entry?.metadata?.description || 'Redo AI action';
    },

    getHistorySummary: () => ({
      total: historySize,
      current: currentPosition,
      canUndo,
      canRedo,
      lastAction: history[currentIndex]?.metadata?.description
    })
  };
};

export default useAIUndoRedo;
