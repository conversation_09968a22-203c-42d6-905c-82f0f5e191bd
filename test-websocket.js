const WebSocket = require('ws');

console.log('Testing WebSocket connection to backend...');

const ws = new WebSocket('ws://localhost:8000/ws/');

ws.on('open', function open() {
  console.log('✅ WebSocket connection established');
  
  // Send a test message
  const testMessage = {
    type: 'ping',
    timestamp: new Date().toISOString(),
    message: 'Hello from test client'
  };
  
  ws.send(JSON.stringify(testMessage));
  console.log('📤 Sent test message:', testMessage);
});

ws.on('message', function message(data) {
  console.log('📥 Received:', data.toString());
});

ws.on('error', function error(err) {
  console.log('❌ WebSocket error:', err.message);
});

ws.on('close', function close() {
  console.log('🔌 WebSocket connection closed');
  process.exit(0);
});

// Close connection after 5 seconds
setTimeout(() => {
  console.log('⏰ Closing connection...');
  ws.close();
}, 5000);
