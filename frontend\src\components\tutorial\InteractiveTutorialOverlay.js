/**
 * Interactive Tutorial Overlay Component
 * 
 * Enhanced tutorial overlay with interactive elements, validation,
 * and step-by-step guidance for complex workflows.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { 
  Button, 
  Progress, 
  Typography, 
  Space, 
  Card, 
  Steps, 
  Tooltip,
  Tag,
  Alert,
  Spin,
  CheckCircleOutlined
} from 'antd';
import {
  CloseOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  BulbOutlined,
  RocketOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled Components
const OverlayContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  pointer-events: ${props => props.allowInteraction ? 'none' : 'auto'};
  transition: all 0.3s ease-in-out;
  backdrop-filter: blur(2px);
`;

const Spotlight = styled.div`
  position: absolute;
  border: 3px solid #1890ff;
  border-radius: 12px;
  box-shadow: 
    0 0 0 4px rgba(24, 144, 255, 0.3),
    0 0 0 9999px rgba(0, 0, 0, 0.6);
  pointer-events: none;
  z-index: 1001;
  transition: all 0.3s ease-in-out;
  animation: ${props => props.animate ? 'pulse' : 'none'} 2s infinite;
  
  @keyframes pulse {
    0% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    50% { 
      box-shadow: 
        0 0 0 8px rgba(24, 144, 255, 0.2),
        0 0 0 16px rgba(24, 144, 255, 0.05),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    100% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
  }
`;

const TutorialCard = styled(Card)`
  position: fixed;
  max-width: 420px;
  min-width: 350px;
  z-index: 1002;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  border: none;
  overflow: hidden;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    border: none;
    padding: 16px 20px;
  }
  
  .ant-card-head-title {
    color: white;
    font-weight: 600;
    font-size: 16px;
  }
  
  .ant-card-body {
    padding: 20px;
  }
`;

const ValidationIndicator = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${props => props.status === 'success' ? '#52c41a' : props.status === 'error' ? '#ff4d4f' : '#faad14'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  z-index: 1003;
  animation: ${props => props.animate ? 'bounce' : 'none'} 0.5s ease-in-out;
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
  }
`;

/**
 * Interactive Tutorial Overlay Component
 */
const InteractiveTutorialOverlay = ({
  isActive = false,
  currentTutorial = null,
  currentStep = null,
  currentStepIndex = 0,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
  onPause,
  onResume,
  isPaused = false,
  validationStatus = null,
  showValidation = false
}) => {
  const [spotlightPosition, setSpotlightPosition] = useState(null);
  const [cardPosition, setCardPosition] = useState({ x: 0, y: 0 });
  const [isValidating, setIsValidating] = useState(false);
  const [validationMessage, setValidationMessage] = useState('');
  
  const overlayRef = useRef(null);
  const cardRef = useRef(null);

  // Calculate spotlight position
  const updateSpotlightPosition = useCallback(() => {
    if (!currentStep?.target || !isActive) {
      setSpotlightPosition(null);
      return;
    }

    const element = document.querySelector(currentStep.target);
    if (element) {
      const rect = element.getBoundingClientRect();
      setSpotlightPosition({
        top: rect.top - 12,
        left: rect.left - 12,
        width: rect.width + 24,
        height: rect.height + 24
      });
    }
  }, [currentStep, isActive]);

  // Calculate card position
  const updateCardPosition = useCallback(() => {
    if (!currentStep || !isActive) return;

    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let x, y;

    // Position based on step type and target
    if (currentStep.target && spotlightPosition) {
      // Position relative to spotlight
      const cardWidth = 420;
      const cardHeight = 300;
      
      // Try to position to the right of spotlight
      if (spotlightPosition.left + spotlightPosition.width + cardWidth + 40 <= viewport.width) {
        x = spotlightPosition.left + spotlightPosition.width + 20;
        y = Math.max(20, Math.min(spotlightPosition.top, viewport.height - cardHeight - 20));
      }
      // Try to position to the left
      else if (spotlightPosition.left - cardWidth - 20 >= 0) {
        x = spotlightPosition.left - cardWidth - 20;
        y = Math.max(20, Math.min(spotlightPosition.top, viewport.height - cardHeight - 20));
      }
      // Position below
      else if (spotlightPosition.top + spotlightPosition.height + cardHeight + 40 <= viewport.height) {
        x = Math.max(20, Math.min(spotlightPosition.left, viewport.width - cardWidth - 20));
        y = spotlightPosition.top + spotlightPosition.height + 20;
      }
      // Position above
      else {
        x = Math.max(20, Math.min(spotlightPosition.left, viewport.width - cardWidth - 20));
        y = Math.max(20, spotlightPosition.top - cardHeight - 20);
      }
    } else {
      // Center the card
      x = viewport.width / 2 - 210;
      y = viewport.height / 2 - 150;
    }

    setCardPosition({ x, y });
  }, [currentStep, isActive, spotlightPosition]);

  // Update positions when step changes
  useEffect(() => {
    updateSpotlightPosition();
  }, [updateSpotlightPosition]);

  useEffect(() => {
    updateCardPosition();
  }, [updateCardPosition]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      updateSpotlightPosition();
      updateCardPosition();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateSpotlightPosition, updateCardPosition]);

  // Handle validation
  useEffect(() => {
    if (showValidation && validationStatus) {
      setIsValidating(false);
      
      if (validationStatus.success) {
        setValidationMessage(validationStatus.message || 'Great job!');
        setTimeout(() => {
          if (onNext) onNext();
        }, 1500);
      } else {
        setValidationMessage(validationStatus.message || 'Try again');
      }
    }
  }, [showValidation, validationStatus, onNext]);

  // Handle step validation
  const handleStepValidation = useCallback(() => {
    if (currentStep?.validation) {
      setIsValidating(true);
      setValidationMessage('Checking...');
      
      // Simulate validation (in real implementation, this would check actual conditions)
      setTimeout(() => {
        const isValid = Math.random() > 0.3; // Simulate validation result
        setIsValidating(false);
        
        if (isValid) {
          setValidationMessage('Perfect! Moving to next step...');
          setTimeout(() => {
            if (onNext) onNext();
          }, 1500);
        } else {
          setValidationMessage('Not quite right. Try again!');
        }
      }, 1000);
    } else {
      if (onNext) onNext();
    }
  }, [currentStep, onNext]);

  // Render tutorial card content
  const renderCardContent = () => {
    if (!currentStep) return null;

    const progress = currentTutorial ? 
      Math.round(((currentStepIndex + 1) / currentTutorial.steps.length) * 100) : 0;

    return (
      <div>
        {/* Progress */}
        <div style={{ marginBottom: 16 }}>
          <Progress 
            percent={progress} 
            size="small" 
            showInfo={false}
            strokeColor={{
              '0%': '#1890ff',
              '100%': '#52c41a',
            }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Step {currentStepIndex + 1} of {currentTutorial?.steps.length || 1}
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {progress}% Complete
            </Text>
          </div>
        </div>

        {/* Step Content */}
        <div style={{ marginBottom: 20 }}>
          <Title level={4} style={{ margin: 0, marginBottom: 8 }}>
            {currentStep.title}
          </Title>
          <Paragraph style={{ margin: 0, fontSize: 14, lineHeight: 1.6 }}>
            {currentStep.content}
          </Paragraph>
        </div>

        {/* Validation Status */}
        {(isValidating || validationMessage) && (
          <Alert
            message={validationMessage}
            type={isValidating ? 'info' : validationStatus?.success ? 'success' : 'warning'}
            icon={isValidating ? <Spin size="small" /> : validationStatus?.success ? <CheckOutlined /> : <ExclamationCircleOutlined />}
            style={{ marginBottom: 16 }}
            showIcon
          />
        )}

        {/* Step Type Specific Content */}
        {currentStep.type === 'interactive' && (
          <div style={{ marginBottom: 16, padding: 12, background: '#f0f9ff', borderRadius: 8 }}>
            <Space>
              <BulbOutlined style={{ color: '#1890ff' }} />
              <Text style={{ fontSize: 13, color: '#1890ff' }}>
                Complete the action to continue
              </Text>
            </Space>
          </div>
        )}

        {/* Navigation */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            {currentStepIndex > 0 && (
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={onPrevious}
                disabled={isValidating}
              >
                Previous
              </Button>
            )}
          </Space>
          
          <Space>
            <Button onClick={onSkip} disabled={isValidating}>
              Skip Tutorial
            </Button>
            
            {isPaused ? (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={onResume}
              >
                Resume
              </Button>
            ) : (
              <Button
                icon={<PauseOutlined />}
                onClick={onPause}
                disabled={isValidating}
              >
                Pause
              </Button>
            )}
            
            {currentStep.type === 'interactive' ? (
              <Button
                type="primary"
                icon={<CheckOutlined />}
                onClick={handleStepValidation}
                loading={isValidating}
              >
                Validate
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<ArrowRightOutlined />}
                onClick={onNext}
                disabled={isValidating}
              >
                {currentStepIndex === (currentTutorial?.steps.length || 1) - 1 ? 'Complete' : 'Next'}
              </Button>
            )}
          </Space>
        </div>
      </div>
    );
  };

  if (!isActive || !currentStep) return null;

  return createPortal(
    <OverlayContainer allowInteraction={currentStep.type === 'interactive'}>
      {/* Spotlight */}
      {spotlightPosition && (
        <Spotlight
          style={spotlightPosition}
          animate={!isPaused}
        />
      )}

      {/* Validation Indicator */}
      {showValidation && validationStatus && spotlightPosition && (
        <ValidationIndicator
          status={validationStatus.success ? 'success' : 'error'}
          animate={true}
          style={{
            left: spotlightPosition.left + spotlightPosition.width - 8,
            top: spotlightPosition.top - 8
          }}
        >
          {validationStatus.success ? <CheckOutlined /> : <ExclamationCircleOutlined />}
        </ValidationIndicator>
      )}

      {/* Tutorial Card */}
      <TutorialCard
        ref={cardRef}
        style={{
          left: cardPosition.x,
          top: cardPosition.y
        }}
        title={
          <Space>
            <RocketOutlined />
            {currentTutorial?.title || 'Tutorial'}
            {currentTutorial?.category && (
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {currentTutorial.category}
              </Tag>
            )}
          </Space>
        }
        extra={
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onSkip}
            style={{ color: 'white' }}
            size="small"
          />
        }
      >
        {renderCardContent()}
      </TutorialCard>
    </OverlayContainer>,
    document.body
  );
};

export default InteractiveTutorialOverlay;
