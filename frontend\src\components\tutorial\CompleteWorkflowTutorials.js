/**
 * Complete Workflow Tutorials
 * 
 * Comprehensive tutorial definitions for complete app creation workflows,
 * from template selection to code export with detailed step-by-step guidance.
 */

// Complete workflow tutorial definitions
export const COMPLETE_WORKFLOW_TUTORIALS = {
  // Beginner: Hello World App
  hello_world_workflow: {
    id: 'hello_world_workflow',
    title: 'Create Your First Hello World App',
    description: 'Complete beginner tutorial: Create a simple Hello World app from start to finish',
    category: 'beginner',
    estimatedDuration: 15,
    difficulty: 'easy',
    prerequisites: [],
    learningObjectives: [
      'Understand the App Builder interface',
      'Learn to add and customize components',
      'Apply basic styling and themes',
      'Preview and export your first app'
    ],
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to App Builder! 👋',
        content: 'Let\'s create your first app together! This tutorial will guide you through building a simple "Hello World" application from scratch. You\'ll learn the basics of using App Builder and have a working app by the end.',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['next'],
        validation: null,
        tips: ['Take your time to explore', 'Don\'t worry about making mistakes', 'You can always restart the tutorial']
      },
      {
        id: 'template_selection',
        title: 'Choose a Template',
        content: 'First, let\'s select a template. Templates give you a head start with pre-designed layouts. For this tutorial, choose the "Blank Template" to start from scratch.',
        target: '[data-tutorial="template-selector"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'template-selected',
          condition: () => document.querySelector('[data-template="blank"]')?.classList.contains('selected'),
          message: 'Great! You\'ve selected the blank template.'
        },
        tips: ['Templates save time', 'You can always change templates later', 'Blank template gives you full control']
      },
      {
        id: 'add_heading',
        title: 'Add a Heading Component',
        content: 'Now let\'s add your first component! Find the "Heading" component in the component palette and drag it to the canvas. This will be the title of your app.',
        target: '[data-tutorial="component-palette"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'component-added',
          componentType: 'heading',
          condition: () => document.querySelectorAll('[data-tutorial="canvas-area"] .heading-component').length > 0,
          message: 'Perfect! You\'ve added a heading component.'
        },
        tips: ['Drag and drop to add components', 'Components are the building blocks', 'You can add multiple components']
      },
      {
        id: 'customize_heading',
        title: 'Customize Your Heading',
        content: 'Click on your heading component to select it, then use the property editor to change the text to "Hello World!" and make it larger.',
        target: '[data-tutorial="property-editor"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'property-changed',
          condition: () => {
            const heading = document.querySelector('[data-tutorial="canvas-area"] .heading-component');
            return heading && heading.textContent.includes('Hello World');
          },
          message: 'Excellent! Your heading now says "Hello World!"'
        },
        tips: ['Click components to select them', 'Property editor shows on the right', 'Try different font sizes']
      },
      {
        id: 'add_text',
        title: 'Add a Text Component',
        content: 'Let\'s add some descriptive text below your heading. Drag a "Text" component from the palette and place it below your heading.',
        target: '[data-tutorial="component-palette"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'component-added',
          componentType: 'text',
          condition: () => document.querySelectorAll('[data-tutorial="canvas-area"] .text-component').length > 0,
          message: 'Great! You\'ve added a text component.'
        },
        tips: ['Position components by dragging', 'Text components are great for descriptions', 'You can add multiple text blocks']
      },
      {
        id: 'customize_text',
        title: 'Customize Your Text',
        content: 'Select your text component and change the content to "Welcome to my first app built with App Builder!" Feel free to adjust the styling too.',
        target: '[data-tutorial="property-editor"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'property-changed',
          condition: () => {
            const text = document.querySelector('[data-tutorial="canvas-area"] .text-component');
            return text && text.textContent.length > 10;
          },
          message: 'Perfect! Your text is now customized.'
        },
        tips: ['Make your text engaging', 'Try different colors and fonts', 'Keep it readable']
      },
      {
        id: 'add_button',
        title: 'Add an Interactive Button',
        content: 'Let\'s make your app interactive! Add a "Button" component below your text. This will make your app more engaging.',
        target: '[data-tutorial="component-palette"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'component-added',
          componentType: 'button',
          condition: () => document.querySelectorAll('[data-tutorial="canvas-area"] .button-component').length > 0,
          message: 'Awesome! You\'ve added an interactive button.'
        },
        tips: ['Buttons make apps interactive', 'You can add click actions later', 'Try different button styles']
      },
      {
        id: 'customize_button',
        title: 'Style Your Button',
        content: 'Select your button and change its text to "Click Me!" and choose a nice color that stands out.',
        target: '[data-tutorial="property-editor"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'property-changed',
          condition: () => {
            const button = document.querySelector('[data-tutorial="canvas-area"] .button-component');
            return button && button.textContent.includes('Click');
          },
          message: 'Excellent! Your button looks great.'
        },
        tips: ['Make buttons stand out', 'Use action words like "Click Me"', 'Choose contrasting colors']
      },
      {
        id: 'apply_theme',
        title: 'Apply a Theme',
        content: 'Now let\'s make your app look professional! Open the Theme Manager and choose a theme that you like. Themes apply consistent styling across your entire app.',
        target: '[data-tutorial="theme-manager"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'theme-applied',
          condition: () => document.querySelector('[data-theme-applied="true"]'),
          message: 'Beautiful! Your app now has a professional theme.'
        },
        tips: ['Themes ensure consistency', 'You can customize themes', 'Try different themes to see what you like']
      },
      {
        id: 'preview_app',
        title: 'Preview Your App',
        content: 'Time to see your creation in action! Click the Preview button to see how your app will look to users. You can interact with it just like a real app.',
        target: '[data-tutorial="preview-mode"]',
        position: 'bottom',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'preview-activated',
          condition: () => document.querySelector('[data-preview-active="true"]'),
          message: 'Amazing! You\'re now previewing your app.'
        },
        tips: ['Preview shows the user experience', 'Test all interactions', 'Check on different screen sizes']
      },
      {
        id: 'test_responsiveness',
        title: 'Test Responsiveness',
        content: 'While in preview mode, try resizing your browser window or use the device preview options to see how your app looks on different screen sizes.',
        target: '[data-tutorial="preview-mode"]',
        position: 'bottom',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'responsiveness-tested',
          condition: () => true, // Auto-pass after delay
          message: 'Great! You\'ve tested your app\'s responsiveness.'
        },
        tips: ['Mobile-first design is important', 'Test on different devices', 'Ensure text is readable on small screens']
      },
      {
        id: 'export_app',
        title: 'Export Your App',
        content: 'Congratulations! Your app is ready. Now let\'s export it so you can use it anywhere. Click the Export button and choose your preferred format (HTML is great for beginners).',
        target: '[data-tutorial="code-export"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'code-exported',
          condition: () => document.querySelector('[data-export-completed="true"]'),
          message: 'Fantastic! You\'ve successfully exported your first app.'
        },
        tips: ['HTML export works everywhere', 'You can host it for free', 'Share your creation with friends']
      },
      {
        id: 'completion',
        title: 'Congratulations! 🎉',
        content: 'You\'ve successfully created and exported your first app! You\'ve learned the basics of App Builder: adding components, customizing properties, applying themes, and exporting. Ready to build something more complex?',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'build-another'],
        validation: null,
        tips: ['You\'re now an App Builder user!', 'Try the intermediate tutorials next', 'Experiment with different components']
      }
    ]
  },

  // Intermediate: Business Landing Page
  business_landing_workflow: {
    id: 'business_landing_workflow',
    title: 'Build a Business Landing Page',
    description: 'Intermediate tutorial: Create a professional business landing page with multiple sections',
    category: 'intermediate',
    estimatedDuration: 25,
    difficulty: 'medium',
    prerequisites: ['hello_world_workflow'],
    learningObjectives: [
      'Create complex layouts with multiple sections',
      'Use advanced components like forms and galleries',
      'Implement responsive design principles',
      'Add professional styling and branding'
    ],
    steps: [
      {
        id: 'welcome',
        title: 'Build a Professional Landing Page 🏢',
        content: 'In this tutorial, you\'ll create a complete business landing page with a header, hero section, features, testimonials, and contact form. This will teach you advanced App Builder techniques.',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['next'],
        validation: null
      },
      {
        id: 'select_template',
        title: 'Choose Business Template',
        content: 'Select the "Business Landing" template to get started with a professional layout structure.',
        target: '[data-tutorial="template-selector"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'template-selected',
          condition: () => document.querySelector('[data-template="business-landing"]')?.classList.contains('selected'),
          message: 'Perfect! Business template selected.'
        }
      },
      // ... more steps for business landing page
      {
        id: 'completion',
        title: 'Professional Landing Page Complete! 🚀',
        content: 'Excellent work! You\'ve built a professional business landing page with multiple sections, forms, and responsive design. You\'re ready for advanced tutorials!',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'try-advanced'],
        validation: null
      }
    ]
  },

  // Advanced: Full-Stack Dashboard
  dashboard_workflow: {
    id: 'dashboard_workflow',
    title: 'Create a Data Dashboard',
    description: 'Advanced tutorial: Build a complete data dashboard with charts, tables, and real-time updates',
    category: 'advanced',
    estimatedDuration: 40,
    difficulty: 'hard',
    prerequisites: ['hello_world_workflow', 'business_landing_workflow'],
    learningObjectives: [
      'Implement data visualization components',
      'Set up real-time data connections',
      'Create interactive dashboard layouts',
      'Add advanced filtering and search',
      'Implement user authentication'
    ],
    steps: [
      {
        id: 'welcome',
        title: 'Build an Advanced Dashboard 📊',
        content: 'This advanced tutorial will teach you to create a sophisticated data dashboard with charts, real-time updates, and interactive features. Perfect for business applications!',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['next'],
        validation: null
      },
      // ... more advanced steps
      {
        id: 'completion',
        title: 'Dashboard Master! 🏆',
        content: 'Incredible! You\'ve mastered App Builder by creating a complex dashboard application. You now have the skills to build professional-grade applications!',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'share-achievement'],
        validation: null
      }
    ]
  }
};

// Workflow tutorial utilities
export const getWorkflowTutorialById = (id) => {
  return COMPLETE_WORKFLOW_TUTORIALS[id] || null;
};

export const getWorkflowTutorialsByCategory = (category) => {
  return Object.values(COMPLETE_WORKFLOW_TUTORIALS).filter(
    tutorial => tutorial.category === category
  );
};

export const getWorkflowTutorialsByDifficulty = (difficulty) => {
  return Object.values(COMPLETE_WORKFLOW_TUTORIALS).filter(
    tutorial => tutorial.difficulty === difficulty
  );
};

export const getRecommendedNextWorkflow = (completedWorkflows) => {
  const completed = new Set(completedWorkflows);

  // Recommend based on progression
  if (!completed.has('hello_world_workflow')) {
    return 'hello_world_workflow';
  }

  if (completed.has('hello_world_workflow') && !completed.has('business_landing_workflow')) {
    return 'business_landing_workflow';
  }

  if (completed.has('business_landing_workflow') && !completed.has('dashboard_workflow')) {
    return 'dashboard_workflow';
  }

  return null; // All workflows completed
};

export const calculateWorkflowProgress = (workflowId, completedSteps) => {
  const workflow = COMPLETE_WORKFLOW_TUTORIALS[workflowId];
  if (!workflow) return 0;

  const totalSteps = workflow.steps.length;
  const completed = completedSteps.filter(stepId =>
    workflow.steps.some(step => step.id === stepId)
  ).length;

  return Math.round((completed / totalSteps) * 100);
};

// Workflow validation helpers
export const validateWorkflowStep = async (stepId, workflowId) => {
  const workflow = COMPLETE_WORKFLOW_TUTORIALS[workflowId];
  const step = workflow?.steps.find(s => s.id === stepId);

  if (!step?.validation) return { success: true, message: 'Step completed!' };

  try {
    // Check validation condition
    const isValid = step.validation.condition ? step.validation.condition() : true;

    return {
      success: isValid,
      message: isValid ?
        (step.validation.message || 'Great job!') :
        'Please complete the required action to continue.'
    };
  } catch (error) {
    console.warn('Validation error:', error);
    return { success: false, message: 'Validation failed. Please try again.' };
  }
};

// Progress tracking for workflows
export const trackWorkflowProgress = (workflowId, stepId, action = 'completed') => {
  const progressKey = `workflow-progress-${workflowId}`;
  const progress = JSON.parse(localStorage.getItem(progressKey) || '{}');

  if (!progress.steps) progress.steps = [];
  if (!progress.startedAt) progress.startedAt = new Date().toISOString();

  switch (action) {
    case 'started':
      progress.currentStep = stepId;
      progress.status = 'in_progress';
      break;
    case 'completed':
      if (!progress.steps.includes(stepId)) {
        progress.steps.push(stepId);
      }
      progress.currentStep = stepId;
      progress.lastActivity = new Date().toISOString();
      break;
    case 'finished':
      progress.status = 'completed';
      progress.completedAt = new Date().toISOString();
      break;
  }

  localStorage.setItem(progressKey, JSON.stringify(progress));
  return progress;
};

// Get workflow progress
export const getWorkflowProgress = (workflowId) => {
  const progressKey = `workflow-progress-${workflowId}`;
  return JSON.parse(localStorage.getItem(progressKey) || '{}');
};

// Achievement system for workflows
export const checkWorkflowAchievements = (workflowId) => {
  const workflow = COMPLETE_WORKFLOW_TUTORIALS[workflowId];
  const progress = getWorkflowProgress(workflowId);

  if (progress.status === 'completed') {
    // Award achievement based on workflow difficulty
    const achievement = {
      id: `workflow-${workflowId}`,
      title: `${workflow.title} Complete!`,
      description: `Completed the ${workflow.difficulty} workflow: ${workflow.title}`,
      category: workflow.category,
      points: workflow.difficulty === 'easy' ? 50 : workflow.difficulty === 'medium' ? 100 : 200,
      earnedAt: new Date().toISOString()
    };

    // Save achievement
    const achievements = JSON.parse(localStorage.getItem('app-builder-achievements') || '[]');
    if (!achievements.find(a => a.id === achievement.id)) {
      achievements.push(achievement);
      localStorage.setItem('app-builder-achievements', JSON.stringify(achievements));

      // Show achievement notification
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('achievementUnlocked', { detail: achievement });
        window.dispatchEvent(event);
      }
    }
  }
};

export default COMPLETE_WORKFLOW_TUTORIALS;
