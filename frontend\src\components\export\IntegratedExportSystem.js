/**
 * Integrated Export System
 * 
 * Handles export functionality for the unified App Builder
 * Generates complete application code with all integrated features
 */

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  Button, 
  Select, 
  Card, 
  Space, 
  Typography, 
  Progress, 
  Alert, 
  Checkbox,
  Input,
  message,
  Tabs
} from 'antd';
import {
  ExportOutlined,
  DownloadOutlined,
  CodeOutlined,
  FileTextOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

// Import unified selectors and actions
import {
  getExportData,
  getExportSettings,
  getValidationErrors,
  isProjectValid
} from '../../redux/selectors/appBuilderSelectors';
import {
  setExportSettings,
  exportProject,
  exportProjectSuccess,
  exportProjectFailure
} from '../../redux/actions/appBuilderActions';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const IntegratedExportSystem = ({ projectId }) => {
  const dispatch = useDispatch();
  
  // Redux state
  const exportData = useSelector(getExportData);
  const exportSettings = useSelector(getExportSettings);
  const validationErrors = useSelector(getValidationErrors);
  const isValid = useSelector(isProjectValid);
  
  // Local state
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportResult, setExportResult] = useState(null);
  const [previewCode, setPreviewCode] = useState('');
  const [activeTab, setActiveTab] = useState('settings');

  // Export frameworks and options
  const frameworks = [
    { value: 'react', label: 'React', description: 'Modern React with hooks and functional components' },
    { value: 'vue', label: 'Vue.js', description: 'Vue 3 with Composition API' },
    { value: 'angular', label: 'Angular', description: 'Angular with TypeScript' },
    { value: 'html', label: 'HTML/CSS/JS', description: 'Vanilla HTML, CSS, and JavaScript' },
    { value: 'next', label: 'Next.js', description: 'React with Next.js framework' },
    { value: 'nuxt', label: 'Nuxt.js', description: 'Vue with Nuxt.js framework' }
  ];

  const codeQualityOptions = [
    { key: 'prettier', label: 'Prettier formatting', description: 'Format code with Prettier' },
    { key: 'eslint', label: 'ESLint rules', description: 'Include ESLint configuration' },
    { key: 'typescript', label: 'TypeScript', description: 'Generate TypeScript code' },
    { key: 'comments', label: 'Code comments', description: 'Include detailed comments' },
    { key: 'documentation', label: 'Documentation', description: 'Generate README and docs' }
  ];

  // Handle export settings change
  const handleSettingsChange = (key, value) => {
    const newSettings = { ...exportSettings, [key]: value };
    dispatch(setExportSettings(newSettings));
  };

  // Handle code quality options change
  const handleCodeQualityChange = (checkedValues) => {
    const codeQuality = {};
    codeQualityOptions.forEach(option => {
      codeQuality[option.key] = checkedValues.includes(option.key);
    });
    handleSettingsChange('codeQuality', codeQuality);
  };

  // Generate preview code
  const generatePreview = () => {
    const { components, layouts, themes, activeTheme } = exportData;
    
    if (exportSettings.framework === 'react') {
      return generateReactPreview(components, layouts, themes, activeTheme);
    } else if (exportSettings.framework === 'vue') {
      return generateVuePreview(components, layouts, themes, activeTheme);
    } else if (exportSettings.framework === 'html') {
      return generateHTMLPreview(components, layouts, themes, activeTheme);
    }
    
    return '// Preview will be generated based on your selections';
  };

  // Generate React preview
  const generateReactPreview = (components, layouts, themes, activeTheme) => {
    const imports = exportSettings.codeQuality.typescript 
      ? "import React from 'react';\nimport './App.css';\n\n"
      : "import React from 'react';\nimport './App.css';\n\n";

    const componentCode = components.map(component => {
      return `const ${component.name} = (${exportSettings.codeQuality.typescript ? 'props: any' : 'props'}) => {
  return (
    <div className="${component.type}-component">
      {/* ${component.name} component */}
      ${JSON.stringify(component.props, null, 2)}
    </div>
  );
};`;
    }).join('\n\n');

    const appCode = `const App = () => {
  return (
    <div className="app" ${activeTheme ? `data-theme="${activeTheme.id}"` : ''}>
      <h1>Generated App</h1>
      ${components.map(c => `<${c.name} />`).join('\n      ')}
    </div>
  );
};

export default App;`;

    return `${imports}${componentCode}\n\n${appCode}`;
  };

  // Generate Vue preview
  const generateVuePreview = (components, layouts, themes, activeTheme) => {
    return `<template>
  <div class="app" ${activeTheme ? `data-theme="${activeTheme.id}"` : ''}>
    <h1>Generated App</h1>
    ${components.map(c => `<${c.name} />`).join('\n    ')}
  </div>
</template>

<script>
${components.map(component => {
  return `import ${component.name} from './components/${component.name}.vue';`;
}).join('\n')}

export default {
  name: 'App',
  components: {
    ${components.map(c => c.name).join(',\n    ')}
  }
};
</script>

<style>
/* Generated styles */
${activeTheme ? generateThemeCSS(activeTheme) : ''}
</style>`;
  };

  // Generate HTML preview
  const generateHTMLPreview = (components, layouts, themes, activeTheme) => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated App</title>
  <style>
    ${activeTheme ? generateThemeCSS(activeTheme) : ''}
  </style>
</head>
<body>
  <div class="app" ${activeTheme ? `data-theme="${activeTheme.id}"` : ''}>
    <h1>Generated App</h1>
    ${components.map(component => `
    <div class="${component.type}-component">
      <!-- ${component.name} -->
      ${JSON.stringify(component.props, null, 2)}
    </div>`).join('')}
  </div>
</body>
</html>`;
  };

  // Generate theme CSS
  const generateThemeCSS = (theme) => {
    return `
:root {
  --primary-color: ${theme.primaryColor};
  --secondary-color: ${theme.secondaryColor};
  --background-color: ${theme.backgroundColor};
  --text-color: ${theme.textColor};
  --font-family: ${theme.fontFamily};
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--background-color);
}`;
  };

  // Handle export
  const handleExport = async () => {
    if (!isValid) {
      message.error('Please fix validation errors before exporting');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressSteps = [
        { step: 20, message: 'Validating project data...' },
        { step: 40, message: 'Generating component code...' },
        { step: 60, message: 'Processing layouts...' },
        { step: 80, message: 'Applying themes...' },
        { step: 100, message: 'Finalizing export...' }
      ];

      for (const { step, message: stepMessage } of progressSteps) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setExportProgress(step);
        message.info(stepMessage);
      }

      // Generate the actual export
      const exportResult = {
        projectId,
        framework: exportSettings.framework,
        files: generateExportFiles(),
        timestamp: new Date().toISOString(),
        settings: exportSettings
      };

      dispatch(exportProjectSuccess(exportResult));
      setExportResult(exportResult);
      message.success('Export completed successfully!');
      
    } catch (error) {
      console.error('Export error:', error);
      dispatch(exportProjectFailure(error.message));
      message.error('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Generate export files
  const generateExportFiles = () => {
    const files = [];
    const { components, layouts, themes, activeTheme } = exportData;

    // Main app file
    files.push({
      name: exportSettings.framework === 'react' ? 'App.jsx' : 
            exportSettings.framework === 'vue' ? 'App.vue' : 'index.html',
      content: generatePreview(),
      type: 'main'
    });

    // Component files
    components.forEach(component => {
      files.push({
        name: `${component.name}.${exportSettings.framework === 'vue' ? 'vue' : 'jsx'}`,
        content: generateComponentFile(component),
        type: 'component'
      });
    });

    // Style files
    if (activeTheme) {
      files.push({
        name: 'theme.css',
        content: generateThemeCSS(activeTheme),
        type: 'style'
      });
    }

    // Package.json for React/Vue projects
    if (['react', 'vue', 'next', 'nuxt'].includes(exportSettings.framework)) {
      files.push({
        name: 'package.json',
        content: generatePackageJson(),
        type: 'config'
      });
    }

    return files;
  };

  // Generate component file
  const generateComponentFile = (component) => {
    if (exportSettings.framework === 'react') {
      return `import React from 'react';

const ${component.name} = (props) => {
  return (
    <div className="${component.type}-component">
      {/* ${component.name} component */}
      {/* Add your component logic here */}
    </div>
  );
};

export default ${component.name};`;
    }
    return `<!-- ${component.name} component -->`;
  };

  // Generate package.json
  const generatePackageJson = () => {
    const dependencies = {
      react: {
        "react": "^18.0.0",
        "react-dom": "^18.0.0"
      },
      vue: {
        "vue": "^3.0.0"
      }
    };

    return JSON.stringify({
      name: "generated-app",
      version: "1.0.0",
      dependencies: dependencies[exportSettings.framework] || {}
    }, null, 2);
  };

  // Download export
  const handleDownload = () => {
    if (!exportResult) return;

    const zip = new JSZip();
    exportResult.files.forEach(file => {
      zip.file(file.name, file.content);
    });

    zip.generateAsync({ type: "blob" }).then(content => {
      const url = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = url;
      a.download = `app-export-${exportSettings.framework}.zip`;
      a.click();
      URL.revokeObjectURL(url);
    });
  };

  // Update preview when settings change
  useEffect(() => {
    setPreviewCode(generatePreview());
  }, [exportSettings, exportData]);

  // Tab items
  const tabItems = [
    {
      key: 'settings',
      label: (
        <Space>
          <SettingOutlined />
          Export Settings
        </Space>
      ),
      children: (
        <ExportSettings 
          settings={exportSettings}
          frameworks={frameworks}
          codeQualityOptions={codeQualityOptions}
          onSettingsChange={handleSettingsChange}
          onCodeQualityChange={handleCodeQualityChange}
        />
      )
    },
    {
      key: 'preview',
      label: (
        <Space>
          <CodeOutlined />
          Code Preview
        </Space>
      ),
      children: (
        <CodePreview 
          code={previewCode}
          framework={exportSettings.framework}
        />
      )
    },
    {
      key: 'validation',
      label: (
        <Space>
          <InfoCircleOutlined />
          Validation
          {!isValid && <span style={{ color: '#ff4d4f' }}>({validationErrors.length})</span>}
        </Space>
      ),
      children: (
        <ValidationPanel 
          errors={validationErrors}
          isValid={isValid}
          exportData={exportData}
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', textAlign: 'center' }}>
        <Title level={2}>
          <ExportOutlined style={{ marginRight: '8px' }} />
          Export Your Application
        </Title>
        <Paragraph>
          Generate production-ready code from your App Builder project
        </Paragraph>
      </div>

      {/* Export Progress */}
      {isExporting && (
        <Card style={{ marginBottom: '24px' }}>
          <Progress 
            percent={exportProgress} 
            status={exportProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <Text type="secondary">Exporting your application...</Text>
        </Card>
      )}

      {/* Export Result */}
      {exportResult && (
        <Alert
          message="Export Completed Successfully!"
          description={
            <Space direction="vertical">
              <Text>Your application has been exported with {exportResult.files.length} files.</Text>
              <Button 
                type="primary" 
                icon={<DownloadOutlined />}
                onClick={handleDownload}
              >
                Download Export
              </Button>
            </Space>
          }
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Main Export Interface */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
        
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Button
            type="primary"
            size="large"
            icon={<ExportOutlined />}
            onClick={handleExport}
            loading={isExporting}
            disabled={!isValid}
          >
            {isExporting ? 'Exporting...' : 'Export Application'}
          </Button>
        </div>
      </Card>
    </div>
  );
};

// Export Settings Component
const ExportSettings = ({ settings, frameworks, codeQualityOptions, onSettingsChange, onCodeQualityChange }) => (
  <Space direction="vertical" style={{ width: '100%' }} size="large">
    <div>
      <Title level={4}>Framework</Title>
      <Select
        value={settings.framework}
        onChange={(value) => onSettingsChange('framework', value)}
        style={{ width: '100%' }}
        size="large"
      >
        {frameworks.map(framework => (
          <Option key={framework.value} value={framework.value}>
            <div>
              <div>{framework.label}</div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {framework.description}
              </Text>
            </div>
          </Option>
        ))}
      </Select>
    </div>

    <div>
      <Title level={4}>Code Quality</Title>
      <Checkbox.Group
        value={Object.keys(settings.codeQuality || {}).filter(key => settings.codeQuality[key])}
        onChange={onCodeQualityChange}
      >
        <Space direction="vertical">
          {codeQualityOptions.map(option => (
            <Checkbox key={option.key} value={option.key}>
              <div>
                <div>{option.label}</div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {option.description}
                </Text>
              </div>
            </Checkbox>
          ))}
        </Space>
      </Checkbox.Group>
    </div>
  </Space>
);

// Code Preview Component
const CodePreview = ({ code, framework }) => (
  <div>
    <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Title level={4}>Code Preview ({framework})</Title>
      <Button
        icon={<CodeOutlined />}
        onClick={() => navigator.clipboard.writeText(code)}
      >
        Copy Code
      </Button>
    </div>
    <TextArea
      value={code}
      readOnly
      rows={20}
      style={{ fontFamily: 'monospace', fontSize: '12px' }}
    />
  </div>
);

// Validation Panel Component
const ValidationPanel = ({ errors, isValid, exportData }) => (
  <Space direction="vertical" style={{ width: '100%' }} size="large">
    {isValid ? (
      <Alert
        message="Project is ready for export"
        description="All validation checks passed successfully."
        type="success"
        showIcon
        icon={<CheckCircleOutlined />}
      />
    ) : (
      <Alert
        message={`${errors.length} validation error${errors.length > 1 ? 's' : ''} found`}
        description="Please fix the following issues before exporting:"
        type="error"
        showIcon
      />
    )}

    {errors.map((error, index) => (
      <Alert
        key={index}
        message={error.message}
        type="warning"
        showIcon
      />
    ))}

    <div>
      <Title level={4}>Project Summary</Title>
      <Space direction="vertical">
        <Text>Components: {exportData.components?.length || 0}</Text>
        <Text>Layouts: {exportData.layouts?.length || 0}</Text>
        <Text>Themes: {exportData.themes?.length || 0}</Text>
        <Text>Active Theme: {exportData.activeTheme?.name || 'None'}</Text>
      </Space>
    </div>
  </Space>
);

export default IntegratedExportSystem;
