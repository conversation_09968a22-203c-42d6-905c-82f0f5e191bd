/**
 * Tutorial System Integration Tests
 * 
 * Comprehensive test suite for the integrated tutorial system,
 * ensuring all components work together seamlessly.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Mock components and utilities
jest.mock('../EnhancedProgressTracker', () => ({
  trackUserProgress: jest.fn(),
  showAchievementNotification: jest.fn()
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      builder: (state = {
        components: [],
        selectedComponent: null,
        canvas: { components: [] },
        theme: { current: 'default' },
        ...initialState
      }) => state
    }
  });
};

describe('Tutorial System Integration', () => {
  let mockStore;
  
  beforeEach(() => {
    mockStore = createMockStore();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    jest.clearAllMocks();
  });

  describe('IntegratedTutorialAssistant', () => {
    it('should render tutorial assistant with all features enabled', async () => {
      const IntegratedTutorialAssistant = require('../IntegratedTutorialAssistant').default;
      
      render(
        <Provider store={mockStore}>
          <IntegratedTutorialAssistant
            enableAutoStart={false}
            showContextualHelp={true}
            features={['tutorial', 'progress', 'achievements']}
          />
        </Provider>
      );

      // Should render the tutorial assistant
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should show tutorial list when activated', async () => {
      const IntegratedTutorialAssistant = require('../IntegratedTutorialAssistant').default;
      
      render(
        <Provider store={mockStore}>
          <IntegratedTutorialAssistant
            enableAutoStart={false}
            showContextualHelp={true}
          />
        </Provider>
      );

      // Click to open tutorial list
      const assistantButton = screen.getByRole('button');
      fireEvent.click(assistantButton);

      await waitFor(() => {
        expect(screen.getByText(/tutorial/i)).toBeInTheDocument();
      });
    });

    it('should handle tutorial completion and track progress', async () => {
      const IntegratedTutorialAssistant = require('../IntegratedTutorialAssistant').default;
      const onTutorialComplete = jest.fn();
      
      render(
        <Provider store={mockStore}>
          <IntegratedTutorialAssistant
            onTutorialComplete={onTutorialComplete}
            enableAutoStart={false}
          />
        </Provider>
      );

      // Simulate tutorial completion
      act(() => {
        // This would normally be triggered by completing a tutorial
        onTutorialComplete({ id: 'getting_started', title: 'Getting Started' });
      });

      expect(onTutorialComplete).toHaveBeenCalledWith({
        id: 'getting_started',
        title: 'Getting Started'
      });
    });
  });

  describe('ContextAwareTutorialTrigger', () => {
    it('should detect user context and trigger appropriate help', async () => {
      const ContextAwareTutorialTrigger = require('../ContextAwareTutorialTrigger').default;
      const onContextualHelp = jest.fn();
      
      // Mock DOM elements for context detection
      document.body.innerHTML = `
        <div data-tutorial="component-palette">
          <div class="component-item">Button</div>
        </div>
        <div data-tutorial="canvas-area"></div>
      `;

      render(
        <ContextAwareTutorialTrigger
          onContextualHelp={onContextualHelp}
          isEnabled={true}
        />
      );

      // Simulate hovering over component palette
      const componentItem = document.querySelector('.component-item');
      fireEvent.mouseEnter(componentItem);

      // Wait for context detection delay
      await waitFor(() => {
        expect(onContextualHelp).toHaveBeenCalled();
      }, { timeout: 3000 });
    });

    it('should track user behavior patterns', async () => {
      const ContextAwareTutorialTrigger = require('../ContextAwareTutorialTrigger').default;
      const onTutorialSuggestion = jest.fn();
      
      render(
        <ContextAwareTutorialTrigger
          onTutorialSuggestion={onTutorialSuggestion}
          isEnabled={true}
        />
      );

      // Simulate multiple component hovers (struggling pattern)
      for (let i = 0; i < 6; i++) {
        act(() => {
          // Simulate component hover behavior
          window.dispatchEvent(new CustomEvent('componentHover'));
        });
      }

      // Should eventually suggest tutorial for struggling users
      await waitFor(() => {
        expect(onTutorialSuggestion).toHaveBeenCalled();
      }, { timeout: 5000 });
    });
  });

  describe('EnhancedContextualHelp', () => {
    it('should show contextual help overlay when triggered', async () => {
      const EnhancedContextualHelp = require('../EnhancedContextualHelp').default;
      
      render(
        <EnhancedContextualHelp
          isEnabled={true}
          showAnimations={true}
        />
      );

      // Trigger contextual help
      act(() => {
        window.dispatchEvent(new CustomEvent('showContextualHelp', {
          detail: {
            text: 'This is helpful information',
            title: 'Help',
            priority: 'medium'
          }
        }));
      });

      await waitFor(() => {
        expect(screen.getByText('This is helpful information')).toBeInTheDocument();
      });
    });

    it('should position help overlay correctly relative to target element', async () => {
      const EnhancedContextualHelp = require('../EnhancedContextualHelp').default;
      
      // Mock target element
      const targetElement = document.createElement('div');
      targetElement.getBoundingClientRect = jest.fn(() => ({
        top: 100,
        left: 100,
        width: 200,
        height: 50,
        bottom: 150,
        right: 300
      }));
      document.body.appendChild(targetElement);

      render(
        <EnhancedContextualHelp isEnabled={true} />
      );

      // Trigger help with target element
      act(() => {
        window.dispatchEvent(new CustomEvent('showContextualHelp', {
          detail: {
            text: 'Positioned help',
            element: targetElement,
            priority: 'high'
          }
        }));
      });

      await waitFor(() => {
        const helpCard = document.querySelector('[style*="position: absolute"]');
        expect(helpCard).toBeInTheDocument();
      });
    });
  });

  describe('WorkflowTutorialManager', () => {
    it('should load and display workflow tutorial correctly', async () => {
      const WorkflowTutorialManager = require('../WorkflowTutorialManager').default;
      
      render(
        <WorkflowTutorialManager
          isActive={true}
          workflowId="hello_world_workflow"
          onComplete={jest.fn()}
          onClose={jest.fn()}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Hello World/i)).toBeInTheDocument();
      });
    });

    it('should handle step navigation correctly', async () => {
      const WorkflowTutorialManager = require('../WorkflowTutorialManager').default;
      const onStepChange = jest.fn();
      
      render(
        <WorkflowTutorialManager
          isActive={true}
          workflowId="hello_world_workflow"
          onStepChange={onStepChange}
          onComplete={jest.fn()}
          onClose={jest.fn()}
        />
      );

      // Find and click next button
      const nextButton = screen.getByText(/next/i);
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(onStepChange).toHaveBeenCalled();
      });
    });

    it('should validate steps before allowing progression', async () => {
      const WorkflowTutorialManager = require('../WorkflowTutorialManager').default;
      
      // Mock validation that fails
      const mockValidation = jest.fn().mockResolvedValue({
        success: false,
        message: 'Please complete the required action'
      });

      render(
        <WorkflowTutorialManager
          isActive={true}
          workflowId="hello_world_workflow"
          onComplete={jest.fn()}
          onClose={jest.fn()}
        />
      );

      // Try to proceed without completing validation
      const nextButton = screen.getByText(/next/i);
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText(/complete the required action/i)).toBeInTheDocument();
      });
    });
  });

  describe('Progress Tracking Integration', () => {
    it('should track component additions for achievements', async () => {
      const { trackUserProgress } = require('../EnhancedProgressTracker');
      
      // Simulate component addition
      act(() => {
        trackUserProgress('component_added', { componentType: 'button' });
      });

      expect(trackUserProgress).toHaveBeenCalledWith('component_added', { componentType: 'button' });
    });

    it('should show achievement notifications when unlocked', async () => {
      const { showAchievementNotification } = require('../EnhancedProgressTracker');
      
      const achievement = {
        id: 'first_component',
        title: 'First Steps',
        description: 'Added your first component'
      };

      act(() => {
        showAchievementNotification(achievement);
      });

      expect(showAchievementNotification).toHaveBeenCalledWith(achievement);
    });

    it('should persist progress data to localStorage', async () => {
      const { trackUserProgress } = require('../EnhancedProgressTracker');
      
      // Mock localStorage.setItem
      localStorageMock.setItem.mockImplementation((key, value) => {
        expect(key).toBe('app-builder-progress');
        expect(JSON.parse(value)).toHaveProperty('componentsAdded');
      });

      act(() => {
        trackUserProgress('component_added');
      });

      expect(localStorageMock.setItem).toHaveBeenCalled();
    });
  });

  describe('Tutorial Settings Integration', () => {
    it('should load and apply tutorial settings', async () => {
      const TutorialSettingsPanel = require('../TutorialSettingsPanel').default;
      const onSettingsChange = jest.fn();
      
      // Mock saved settings
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        enabled: true,
        showContextualHelp: false,
        autoAdvance: true
      }));

      render(
        <TutorialSettingsPanel
          onSettingsChange={onSettingsChange}
          onClose={jest.fn()}
        />
      );

      // Settings should be loaded from localStorage
      expect(localStorageMock.getItem).toHaveBeenCalledWith('app-builder-tutorial-settings');
    });

    it('should save settings when changed', async () => {
      const TutorialSettingsPanel = require('../TutorialSettingsPanel').default;
      const onSettingsChange = jest.fn();
      
      render(
        <TutorialSettingsPanel
          onSettingsChange={onSettingsChange}
          onClose={jest.fn()}
        />
      );

      // Find and click save button
      const saveButton = screen.getByText(/save/i);
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(localStorageMock.setItem).toHaveBeenCalledWith(
          'app-builder-tutorial-settings',
          expect.any(String)
        );
      });
    });
  });

  describe('End-to-End Tutorial Flow', () => {
    it('should complete full tutorial workflow from start to finish', async () => {
      // This would be a comprehensive integration test
      // that simulates a user going through an entire tutorial
      
      const IntegratedAppBuilder = require('../../builder/IntegratedAppBuilder').default;
      
      render(
        <Provider store={mockStore}>
          <IntegratedAppBuilder
            enableFeatures={{
              tutorial: true,
              tutorialAssistant: true,
              progress: true
            }}
          />
        </Provider>
      );

      // Start workflow tutorial
      const workflowButton = screen.getByText(/workflow/i);
      fireEvent.click(workflowButton);

      // Should start the tutorial system
      await waitFor(() => {
        expect(screen.getByText(/hello world/i)).toBeInTheDocument();
      });

      // This would continue with step-by-step simulation
      // of user interactions through the complete workflow
    });
  });
});

// Test utilities
export const createTestTutorialStep = (overrides = {}) => ({
  id: 'test-step',
  title: 'Test Step',
  content: 'This is a test step',
  target: '[data-test="target"]',
  position: 'bottom',
  type: 'interactive',
  validation: {
    type: 'test-validation',
    condition: () => true,
    message: 'Test validation passed'
  },
  ...overrides
});

export const createTestWorkflow = (overrides = {}) => ({
  id: 'test-workflow',
  title: 'Test Workflow',
  description: 'A test workflow for testing',
  category: 'test',
  estimatedDuration: 5,
  difficulty: 'easy',
  steps: [createTestTutorialStep()],
  ...overrides
});

export const mockTutorialContext = {
  isActive: false,
  currentTutorial: null,
  currentStep: null,
  completedTutorials: new Set(),
  startTutorial: jest.fn(),
  completeTutorial: jest.fn(),
  skipTutorial: jest.fn()
};
