#!/usr/bin/env node

/**
 * Syntax Highlighting Test Script
 * Tests react-syntax-highlighter functionality
 */

const fs = require('fs');
const path = require('path');

console.log('=== React Syntax Highlighter Test ===\n');

// Test 1: Check if react-syntax-highlighter is installed
console.log('1. Checking package installation...');
try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  const version = packageJson.dependencies['react-syntax-highlighter'];
  if (version) {
    console.log(`✅ react-syntax-highlighter installed: ${version}`);
  } else {
    console.log('❌ react-syntax-highlighter not found in dependencies');
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  process.exit(1);
}

// Test 2: Check if the package can be required
console.log('\n2. Testing package import...');
try {
  const syntaxHighlighter = require('react-syntax-highlighter');
  console.log('✅ react-syntax-highlighter can be imported');
  
  // Check for Prism component
  if (syntaxHighlighter.Prism) {
    console.log('✅ Prism component available');
  } else {
    console.log('⚠️  Prism component not found in main export');
  }
} catch (error) {
  console.log('❌ Error importing react-syntax-highlighter:', error.message);
}

// Test 3: Check specific imports used in the application
console.log('\n3. Testing specific imports...');
try {
  // Test Prism import
  const { Prism } = require('react-syntax-highlighter');
  console.log('✅ Prism component can be imported');
} catch (error) {
  console.log('❌ Error importing Prism:', error.message);
}

try {
  // Test style imports
  const tomorrow = require('react-syntax-highlighter/dist/esm/styles/prism/tomorrow');
  console.log('✅ tomorrow style can be imported');
} catch (error) {
  console.log('❌ Error importing tomorrow style:', error.message);
  console.log('   Trying alternative import path...');
  try {
    const tomorrow = require('react-syntax-highlighter/dist/cjs/styles/prism/tomorrow');
    console.log('✅ tomorrow style imported from CJS path');
  } catch (error2) {
    console.log('❌ Error importing tomorrow style from CJS:', error2.message);
  }
}

try {
  const vscDarkPlus = require('react-syntax-highlighter/dist/esm/styles/prism/vsc-dark-plus');
  console.log('✅ vsc-dark-plus style can be imported');
} catch (error) {
  console.log('❌ Error importing vsc-dark-plus style:', error.message);
  console.log('   Trying alternative import path...');
  try {
    const vscDarkPlus = require('react-syntax-highlighter/dist/cjs/styles/prism/vsc-dark-plus');
    console.log('✅ vsc-dark-plus style imported from CJS path');
  } catch (error2) {
    console.log('❌ Error importing vsc-dark-plus style from CJS:', error2.message);
  }
}

// Test 4: Check files that use react-syntax-highlighter
console.log('\n4. Checking application files...');

const filesToCheck = [
  './src/components/export/ExportPreview.js',
  './src/pages/TemplateSystemDemo.js',
  './src/components/preview/ComponentPreview.js'
];

filesToCheck.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`✅ Found: ${filePath}`);
    
    // Check imports in the file
    const content = fs.readFileSync(filePath, 'utf8');
    if (content.includes('react-syntax-highlighter')) {
      console.log(`   ✅ Contains react-syntax-highlighter import`);
      
      // Check specific imports
      if (content.includes('Prism as SyntaxHighlighter')) {
        console.log(`   ✅ Uses Prism as SyntaxHighlighter`);
      }
      if (content.includes('tomorrow')) {
        console.log(`   ✅ Imports tomorrow style`);
      }
      if (content.includes('vscDarkPlus')) {
        console.log(`   ✅ Imports vscDarkPlus style`);
      }
    } else {
      console.log(`   ❌ Does not contain react-syntax-highlighter import`);
    }
  } else {
    console.log(`❌ Not found: ${filePath}`);
  }
});

// Test 5: Check node_modules
console.log('\n5. Checking node_modules...');
const nodeModulesPath = './node_modules/react-syntax-highlighter';
if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ react-syntax-highlighter exists in node_modules');
  
  // Check for dist directory
  const distPath = path.join(nodeModulesPath, 'dist');
  if (fs.existsSync(distPath)) {
    console.log('✅ dist directory exists');
    
    // Check for esm and cjs directories
    const esmPath = path.join(distPath, 'esm');
    const cjsPath = path.join(distPath, 'cjs');
    
    if (fs.existsSync(esmPath)) {
      console.log('✅ ESM build exists');
    } else {
      console.log('❌ ESM build not found');
    }
    
    if (fs.existsSync(cjsPath)) {
      console.log('✅ CJS build exists');
    } else {
      console.log('❌ CJS build not found');
    }
  } else {
    console.log('❌ dist directory not found');
  }
} else {
  console.log('❌ react-syntax-highlighter not found in node_modules');
}

console.log('\n=== Test Complete ===');
