import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, Typography, Alert, Tag, Divider } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  LoadingOutlined,
  ReloadOutlined 
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

/**
 * WebSocket Quick Test Component
 * 
 * A simple component to quickly test WebSocket connectivity
 * and diagnose connection issues.
 */
const WebSocketQuickTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const wsRef = useRef(null);

  // Test URLs to check
  const testUrls = [
    'ws://localhost:3000/ws/',
    'ws://localhost:8000/ws/',
    'ws://localhost:3000/ws/app_builder/',
    'ws://localhost:8000/ws/app_builder/'
  ];

  const addTestResult = (url, status, message, error = null) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      url,
      status,
      message,
      error,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testWebSocketConnection = async (url) => {
    return new Promise((resolve) => {
      try {
        const ws = new WebSocket(url);
        const timeout = setTimeout(() => {
          ws.close();
          resolve({
            success: false,
            message: 'Connection timeout (5s)',
            error: 'TIMEOUT'
          });
        }, 5000);

        ws.onopen = () => {
          clearTimeout(timeout);
          ws.close();
          resolve({
            success: true,
            message: 'Connection successful',
            error: null
          });
        };

        ws.onerror = (error) => {
          clearTimeout(timeout);
          resolve({
            success: false,
            message: 'Connection failed',
            error: error.message || 'Unknown error'
          });
        };

        ws.onclose = (event) => {
          if (event.code !== 1000) {
            clearTimeout(timeout);
            resolve({
              success: false,
              message: `Connection closed with code: ${event.code}`,
              error: event.reason || 'Connection closed unexpectedly'
            });
          }
        };
      } catch (error) {
        resolve({
          success: false,
          message: 'Failed to create WebSocket',
          error: error.message
        });
      }
    });
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    setConnectionStatus('testing');

    for (const url of testUrls) {
      const result = await testWebSocketConnection(url);
      addTestResult(
        url,
        result.success ? 'success' : 'error',
        result.message,
        result.error
      );
    }

    setIsLoading(false);
    setConnectionStatus('completed');
  };

  const clearResults = () => {
    setTestResults([]);
    setConnectionStatus('disconnected');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'testing':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      default:
        return null;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'testing':
        return 'processing';
      default:
        return 'default';
    }
  };

  useEffect(() => {
    // Run initial test on component mount
    runAllTests();
  }, []);

  return (
    <Card 
      title={
        <Space>
          <Title level={4} style={{ margin: 0 }}>
            WebSocket Quick Test
          </Title>
          {connectionStatus === 'testing' && <LoadingOutlined />}
        </Space>
      }
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />}
            onClick={runAllTests}
            loading={isLoading}
          >
            Test Again
          </Button>
          <Button onClick={clearResults}>
            Clear
          </Button>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {connectionStatus === 'testing' && (
          <Alert
            message="Testing WebSocket connections..."
            type="info"
            showIcon
            icon={<LoadingOutlined />}
          />
        )}

        {testResults.length > 0 && (
          <>
            <Divider orientation="left">Test Results</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              {testResults.map((result) => (
                <Card 
                  key={result.id}
                  size="small"
                  style={{ 
                    borderLeft: `4px solid ${
                      result.status === 'success' ? '#52c41a' : '#ff4d4f'
                    }` 
                  }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Space>
                      {getStatusIcon(result.status)}
                      <Text strong>{result.url}</Text>
                      <Tag color={getStatusColor(result.status)}>
                        {result.status.toUpperCase()}
                      </Tag>
                      <Text type="secondary">{result.timestamp}</Text>
                    </Space>
                    
                    <Text>{result.message}</Text>
                    
                    {result.error && (
                      <Text type="danger" code>
                        Error: {result.error}
                      </Text>
                    )}
                  </Space>
                </Card>
              ))}
            </Space>
          </>
        )}

        {connectionStatus === 'completed' && (
          <>
            <Divider />
            <Space direction="vertical">
              <Title level={5}>Summary</Title>
              <Paragraph>
                <Text>
                  Successful connections: {testResults.filter(r => r.status === 'success').length} / {testResults.length}
                </Text>
              </Paragraph>
              
              {testResults.some(r => r.status === 'success') ? (
                <Alert
                  message="WebSocket connectivity is working!"
                  description="At least one WebSocket endpoint is accessible."
                  type="success"
                  showIcon
                />
              ) : (
                <Alert
                  message="WebSocket connectivity issues detected"
                  description="No WebSocket endpoints are accessible. Check if the backend server is running."
                  type="error"
                  showIcon
                />
              )}
            </Space>
          </>
        )}
      </Space>
    </Card>
  );
};

export default WebSocketQuickTest;
