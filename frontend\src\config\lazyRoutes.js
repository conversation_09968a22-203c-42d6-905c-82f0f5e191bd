import React from 'react';
import { createLazyComponent, LoadingStates } from '../utils/lazyLoading';

/**
 * Lazy Route Configuration for Bundle Size Optimization
 * All major routes are lazy-loaded to reduce initial bundle size
 */

// Main App Builder Routes (highest priority)
export const AppBuilderIntegrated = createLazyComponent(
  () => import('../pages/AppBuilderIntegrated'),
  {
    componentName: 'AppBuilderIntegrated',
    fallback: LoadingStates.withDescription('Loading App Builder...'),
    retryAttempts: 3,
    preload: false // Load only when needed
  }
);

export const AppBuilderMVP = createLazyComponent(
  () => import('../pages/AppBuilderMVP'),
  {
    componentName: 'AppBuilderMVP',
    fallback: LoadingStates.withDescription('Loading MVP Builder...'),
    retryAttempts: 3,
    preload: false
  }
);

export const AppBuilderPage = createLazyComponent(
  () => import('../pages/AppBuilderPage'),
  {
    componentName: 'AppBuilderPage',
    fallback: LoadingStates.standard,
    retryAttempts: 2,
    preload: false
  }
);

// Secondary Routes (lower priority)
export const HomePage = createLazyComponent(
  () => import('../pages/HomePage'),
  {
    componentName: 'HomePage',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

export const DashboardPage = createLazyComponent(
  () => import('../pages/DashboardPage'),
  {
    componentName: 'DashboardPage',
    fallback: LoadingStates.standard,
    retryAttempts: 2,
    preload: false
  }
);

// Diagnostic and Testing Routes (lowest priority)
export const WebSocketDiagnosticsPage = createLazyComponent(
  () => import('../pages/WebSocketDiagnosticsPage'),
  {
    componentName: 'WebSocketDiagnosticsPage',
    fallback: LoadingStates.minimal,
    retryAttempts: 1,
    preload: false
  }
);

export const WebSocketTestingPage = createLazyComponent(
  () => import('../pages/WebSocketTestingPage'),
  {
    componentName: 'WebSocketTestingPage',
    fallback: LoadingStates.minimal,
    retryAttempts: 1,
    preload: false
  }
);

export const NetworkDiagnosticPage = createLazyComponent(
  () => import('../pages/NetworkDiagnosticPage'),
  {
    componentName: 'NetworkDiagnosticPage',
    fallback: LoadingStates.minimal,
    retryAttempts: 1,
    preload: false
  }
);

export const DataVisualizationPage = createLazyComponent(
  () => import('../pages/DataVisualizationPage'),
  {
    componentName: 'DataVisualizationPage',
    fallback: LoadingStates.minimal,
    retryAttempts: 1,
    preload: false
  }
);

// Route groups for progressive loading
export const CoreRoutes = [
  AppBuilderIntegrated,
  AppBuilderMVP,
  HomePage
];

export const SecondaryRoutes = [
  AppBuilderPage,
  DashboardPage
];

export const DiagnosticRoutes = [
  WebSocketDiagnosticsPage,
  WebSocketTestingPage,
  NetworkDiagnosticPage,
  DataVisualizationPage
];

// Route configuration for React Router
export const routeConfig = [
  {
    path: '/',
    component: HomePage,
    exact: true,
    priority: 'high'
  },
  {
    path: '/app-builder',
    component: AppBuilderIntegrated,
    exact: true,
    priority: 'high'
  },
  {
    path: '/app-builder-mvp',
    component: AppBuilderMVP,
    exact: true,
    priority: 'high'
  },
  {
    path: '/builder',
    component: AppBuilderPage,
    exact: true,
    priority: 'medium'
  },
  {
    path: '/dashboard',
    component: DashboardPage,
    exact: true,
    priority: 'medium'
  },
  {
    path: '/websocket-diagnostics',
    component: WebSocketDiagnosticsPage,
    exact: true,
    priority: 'low'
  },
  {
    path: '/websocket-testing',
    component: WebSocketTestingPage,
    exact: true,
    priority: 'low'
  },
  {
    path: '/network-diagnostic',
    component: NetworkDiagnosticPage,
    exact: true,
    priority: 'low'
  },
  {
    path: '/data-visualization',
    component: DataVisualizationPage,
    exact: true,
    priority: 'low'
  }
];

// Preload strategy based on route priority
export const preloadStrategy = {
  immediate: [], // No immediate preloading to minimize initial bundle
  onIdle: CoreRoutes.slice(1), // Preload core routes when browser is idle
  onInteraction: SecondaryRoutes, // Preload on user interaction
  onDemand: DiagnosticRoutes // Load only when accessed
};

export default {
  AppBuilderIntegrated,
  AppBuilderMVP,
  AppBuilderPage,
  HomePage,
  DashboardPage,
  WebSocketDiagnosticsPage,
  WebSocketTestingPage,
  NetworkDiagnosticPage,
  DataVisualizationPage,
  routeConfig,
  preloadStrategy
};
