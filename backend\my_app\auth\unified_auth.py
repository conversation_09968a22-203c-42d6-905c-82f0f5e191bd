"""
Unified Authentication System for App Builder
Provides consistent JWT token handling across the application
"""

from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from .jwt_auth import generate_jwt_token
from ..error_handling import error_response, handle_exception
from ..security import add_security_headers, sanitize_request, get_sanitized_data
from ..rate_limiting import rate_limit
import jwt
import datetime
import logging

logger = logging.getLogger(__name__)



class CsrfExemptSessionAuthentication(SessionAuthentication):
    """
    Custom authentication class that disables CSRF protection.
    """
    def enforce_csrf(self, request):
        return  # To not perform the csrf check previously happening


def generate_refresh_token(user):
    """
    Generate a refresh token for the given user.
    """
    payload = {
        'user_id': user.id,
        'type': 'refresh',
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=30),  # 30 days
        'iat': datetime.datetime.utcnow()
    }
    
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm='HS256')


def verify_refresh_token(token):
    """
    Verify and decode a refresh token.
    """
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=['HS256'])
        
        if payload.get('type') != 'refresh':
            raise jwt.InvalidTokenError('Invalid token type')
            
        return payload
    except jwt.ExpiredSignatureError:
        raise jwt.ExpiredSignatureError('Refresh token has expired')
    except jwt.InvalidTokenError:
        raise jwt.InvalidTokenError('Invalid refresh token')


@api_view(['POST'])
@authentication_classes([CsrfExemptSessionAuthentication])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=5, period=60, scope='ip')
def login(request):
    """
    Unified login endpoint that returns both access and refresh tokens.
    """
    username = get_sanitized_data(request, 'username')
    password = get_sanitized_data(request, 'password')

    if not username or not password:
        return error_response(
            'MISSING_CREDENTIALS',
            'Username and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Authenticate the user
    user = authenticate(username=username, password=password)
    if not user:
        logger.warning(f"Failed login attempt for user: {username}")
        return error_response(
            'INVALID_CREDENTIALS',
            'Invalid username or password',
            status.HTTP_401_UNAUTHORIZED
        )

    if not user.is_active:
        return error_response(
            'INACTIVE_USER',
            'User account is inactive',
            status.HTTP_401_UNAUTHORIZED
        )

    # Generate tokens
    access_token = generate_jwt_token(user)
    refresh_token = generate_refresh_token(user)

    # Return unified response
    return Response({
        'access_token': access_token,
        'refresh_token': refresh_token,
        'token_type': 'Bearer',
        'expires_in': settings.JWT_EXPIRATION_DELTA,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    })


@api_view(['POST'])
@authentication_classes([CsrfExemptSessionAuthentication])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=10, period=60, scope='ip')
def refresh_token(request):
    """
    Refresh access token using refresh token.
    """
    refresh_token_value = get_sanitized_data(request, 'refresh_token')

    if not refresh_token_value:
        return error_response(
            'MISSING_TOKEN',
            'Refresh token is required',
            status.HTTP_400_BAD_REQUEST
        )

    try:
        # Verify refresh token
        payload = verify_refresh_token(refresh_token_value)
        
        # Get user
        user = User.objects.get(id=payload['user_id'])
        
        if not user.is_active:
            return error_response(
                'INACTIVE_USER',
                'User account is inactive',
                status.HTTP_401_UNAUTHORIZED
            )

        # Generate new access token
        new_access_token = generate_jwt_token(user)
        
        # Optionally generate new refresh token (for rotation)
        new_refresh_token = generate_refresh_token(user)

        return Response({
            'access_token': new_access_token,
            'refresh_token': new_refresh_token,
            'token_type': 'Bearer',
            'expires_in': settings.JWT_EXPIRATION_DELTA
        })

    except jwt.ExpiredSignatureError:
        return error_response(
            'TOKEN_EXPIRED',
            'Refresh token has expired',
            status.HTTP_401_UNAUTHORIZED
        )
    except jwt.InvalidTokenError:
        return error_response(
            'INVALID_TOKEN',
            'Invalid refresh token',
            status.HTTP_401_UNAUTHORIZED
        )
    except User.DoesNotExist:
        return error_response(
            'USER_NOT_FOUND',
            'User not found',
            status.HTTP_401_UNAUTHORIZED
        )


@api_view(['POST'])
@authentication_classes([CsrfExemptSessionAuthentication])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=3, period=60, scope='ip')
def register(request):
    """
    Register a new user and return tokens.
    """
    username = get_sanitized_data(request, 'username')
    email = get_sanitized_data(request, 'email')
    password = get_sanitized_data(request, 'password')
    first_name = get_sanitized_data(request, 'first_name', '')
    last_name = get_sanitized_data(request, 'last_name', '')

    if not username or not email or not password:
        return error_response(
            'MISSING_FIELDS',
            'Username, email, and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if user already exists
    if User.objects.filter(username=username).exists():
        return error_response(
            'USER_EXISTS',
            'Username already exists',
            status.HTTP_400_BAD_REQUEST
        )

    if User.objects.filter(email=email).exists():
        return error_response(
            'EMAIL_EXISTS',
            'Email already exists',
            status.HTTP_400_BAD_REQUEST
        )

    try:
        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )

        # Generate tokens
        access_token = generate_jwt_token(user)
        refresh_token = generate_refresh_token(user)

        logger.info(f"New user registered: {username}")

        return Response({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': settings.JWT_EXPIRATION_DELTA,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_staff': user.is_staff
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"User registration failed: {str(e)}")
        return error_response(
            'REGISTRATION_FAILED',
            'User registration failed',
            status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@handle_exception
@add_security_headers
def logout(request):
    """
    Logout user (client-side token removal).
    """
    # In a stateless JWT system, logout is primarily client-side
    # We could implement token blacklisting here if needed
    
    return Response({
        'message': 'Successfully logged out'
    })


@api_view(['GET'])
@handle_exception
@add_security_headers
def verify_token(request):
    """
    Verify if the current token is valid and return user info.
    """
    user = request.user
    
    return Response({
        'valid': True,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    })
