/**
 * Test component to verify IntegratedAppBuilder loads correctly
 */

import React from 'react';
import { <PERSON><PERSON>, <PERSON>ton, Card, Typography } from 'antd';

const { Title, Text } = Typography;

export default function IntegratedAppBuilderTest() {
  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>IntegratedAppBuilder Test</Title>
        <Alert
          message="Test Component Loaded Successfully"
          description="This test component confirms that the import system is working. The IntegratedAppBuilder should load next."
          type="success"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        
        <Text>
          If you see this message, it means the component loading system is working correctly.
          The IntegratedAppBuilder should be accessible via the "Try Enhanced Builder" button.
        </Text>
        
        <div style={{ marginTop: '16px' }}>
          <Button type="primary" onClick={() => window.location.reload()}>
            Reload Page
          </Button>
        </div>
      </Card>
    </div>
  );
}
