/**
 * App Builder Selectors
 * 
 * Unified selectors for App Builder features integration
 * Provides optimized data access for components, layouts, themes, and export functionality
 */

import { createSelector } from 'reselect';

// Base selectors
const getAppBuilderState = (state) => state.appBuilder || {};

// Component selectors
export const getComponents = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.components || []
);

export const getSelectedComponent = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.selectedComponent
);

export const getDraggedComponent = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.draggedComponent
);

export const getComponentById = createSelector(
  [getComponents, (state, componentId) => componentId],
  (components, componentId) => components.find(component => component.id === componentId)
);

export const getComponentsByType = createSelector(
  [getComponents, (state, componentType) => componentType],
  (components, componentType) => components.filter(component => component.type === componentType)
);

// Layout selectors
export const getLayouts = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.layouts || []
);

export const getSelectedLayout = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.selectedLayout
);

export const getLayoutById = createSelector(
  [getLayouts, (state, layoutId) => layoutId],
  (layouts, layoutId) => layouts.find(layout => layout.id === layoutId)
);

export const getLayoutWithComponents = createSelector(
  [getLayoutById, getComponents],
  (layout, components) => {
    if (!layout) return null;
    
    return {
      ...layout,
      items: (layout.items || []).map(item => ({
        ...item,
        component: components.find(c => c.id === item.componentId)
      }))
    };
  }
);

export const getLayoutsWithComponents = createSelector(
  [getLayouts, getComponents],
  (layouts, components) => layouts.map(layout => ({
    ...layout,
    items: (layout.items || []).map(item => ({
      ...item,
      component: components.find(c => c.id === item.componentId)
    }))
  }))
);

// Theme selectors
export const getThemes = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.themes || []
);

export const getActiveTheme = createSelector(
  [getAppBuilderState, getThemes],
  (appBuilder, themes) => {
    if (!appBuilder.activeTheme) return null;
    return themes.find(theme => theme.id === appBuilder.activeTheme);
  }
);

export const getThemeById = createSelector(
  [getThemes, (state, themeId) => themeId],
  (themes, themeId) => themes.find(theme => theme.id === themeId)
);

export const getComponentsWithThemes = createSelector(
  [getComponents, getThemes],
  (components, themes) => components.map(component => ({
    ...component,
    theme: component.themeId ? themes.find(t => t.id === component.themeId) : null
  }))
);

export const getLayoutsWithThemes = createSelector(
  [getLayouts, getThemes],
  (layouts, themes) => layouts.map(layout => ({
    ...layout,
    theme: layout.themeId ? themes.find(t => t.id === layout.themeId) : null
  }))
);

// Project selectors
export const getProjects = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.projects || []
);

export const getActiveProject = createSelector(
  [getAppBuilderState, getProjects],
  (appBuilder, projects) => {
    if (!appBuilder.activeProject) return null;
    return projects.find(project => project.id === appBuilder.activeProject);
  }
);

export const getProjectById = createSelector(
  [getProjects, (state, projectId) => projectId],
  (projects, projectId) => projects.find(project => project.id === projectId)
);

// Export selectors
export const getExportSettings = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.exportSettings || {}
);

export const getExportHistory = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.exportHistory || []
);

export const getExportData = createSelector(
  [getComponents, getLayouts, getThemes, getActiveTheme, getExportSettings],
  (components, layouts, themes, activeTheme, exportSettings) => ({
    components,
    layouts,
    themes,
    activeTheme,
    exportSettings,
    metadata: {
      generatedAt: new Date().toISOString(),
      version: '2.0',
      totalComponents: components.length,
      totalLayouts: layouts.length,
      totalThemes: themes.length
    }
  })
);

// UI state selectors
export const getActiveFeature = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.activeFeature || 'components'
);

export const getPreviewMode = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.previewMode || false
);

// History selectors
export const getHistory = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.history || { past: [], present: null, future: [] }
);

export const canUndo = createSelector(
  [getHistory],
  (history) => history.past.length > 0
);

export const canRedo = createSelector(
  [getHistory],
  (history) => history.future.length > 0
);

// Complex selectors for integrated workflows
export const getCompleteProjectData = createSelector(
  [getComponents, getLayouts, getThemes, getActiveTheme, getActiveProject],
  (components, layouts, themes, activeTheme, activeProject) => ({
    project: activeProject,
    components,
    layouts: layouts.map(layout => ({
      ...layout,
      items: (layout.items || []).map(item => ({
        ...item,
        component: components.find(c => c.id === item.componentId)
      }))
    })),
    themes,
    activeTheme,
    stats: {
      totalComponents: components.length,
      totalLayouts: layouts.length,
      totalThemes: themes.length,
      componentsInLayouts: layouts.reduce((acc, layout) => acc + (layout.items?.length || 0), 0)
    }
  })
);

export const getComponentsNotInLayouts = createSelector(
  [getComponents, getLayouts],
  (components, layouts) => {
    const usedComponentIds = new Set();
    layouts.forEach(layout => {
      (layout.items || []).forEach(item => {
        usedComponentIds.add(item.componentId);
      });
    });
    
    return components.filter(component => !usedComponentIds.has(component.id));
  }
);

export const getLayoutsUsingComponent = createSelector(
  [getLayouts, (state, componentId) => componentId],
  (layouts, componentId) => layouts.filter(layout =>
    (layout.items || []).some(item => item.componentId === componentId)
  )
);

export const getThemeUsageStats = createSelector(
  [getComponents, getLayouts, getThemes],
  (components, layouts, themes) => {
    const themeUsage = {};
    
    themes.forEach(theme => {
      themeUsage[theme.id] = {
        theme,
        componentsCount: components.filter(c => c.themeId === theme.id).length,
        layoutsCount: layouts.filter(l => l.themeId === theme.id).length
      };
    });
    
    return themeUsage;
  }
);

// Loading and error selectors
export const getLoading = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.loading || false
);

export const getError = createSelector(
  [getAppBuilderState],
  (appBuilder) => appBuilder.error
);

// Validation selectors
export const getValidationErrors = createSelector(
  [getComponents, getLayouts],
  (components, layouts) => {
    const errors = [];
    
    // Check for components without names
    components.forEach(component => {
      if (!component.name || component.name.trim() === '') {
        errors.push({
          type: 'component',
          id: component.id,
          message: 'Component name is required'
        });
      }
    });
    
    // Check for layouts with missing components
    layouts.forEach(layout => {
      (layout.items || []).forEach(item => {
        const component = components.find(c => c.id === item.componentId);
        if (!component) {
          errors.push({
            type: 'layout',
            id: layout.id,
            message: `Layout "${layout.name}" references missing component: ${item.componentId}`
          });
        }
      });
    });
    
    return errors;
  }
);

export const isProjectValid = createSelector(
  [getValidationErrors],
  (errors) => errors.length === 0
);
