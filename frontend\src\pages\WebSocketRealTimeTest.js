import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Space, Button, Input, List, Tag, Alert, Divider } from 'antd';
import { 
  SendOutlined, 
  DisconnectOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * WebSocket Real-time Test Page
 * Tests WebSocket connectivity and real-time communication features
 */
const WebSocketRealTimeTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [wsUrl, setWsUrl] = useState('ws://localhost:3000/ws/');
  const [lastPing, setLastPing] = useState(null);
  const [latency, setLatency] = useState(null);
  const wsRef = useRef(null);
  const pingIntervalRef = useRef(null);

  // Connect to WebSocket
  const connect = () => {
    try {
      setConnectionStatus('connecting');
      addMessage('system', 'Attempting to connect...');
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        setConnectionStatus('connected');
        addMessage('system', 'Connected successfully!');
        startPingTest();
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Handle ping response for latency calculation
          if (data.type === 'pong' && lastPing) {
            const currentLatency = Date.now() - lastPing;
            setLatency(currentLatency);
            addMessage('system', `Pong received - Latency: ${currentLatency}ms`);
            return;
          }
          
          addMessage('received', JSON.stringify(data, null, 2));
        } catch (error) {
          addMessage('received', event.data);
        }
      };
      
      wsRef.current.onclose = (event) => {
        setConnectionStatus('disconnected');
        addMessage('system', `Connection closed: ${event.code} - ${event.reason || 'No reason provided'}`);
        stopPingTest();
      };
      
      wsRef.current.onerror = (error) => {
        setConnectionStatus('error');
        addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
      };
      
    } catch (error) {
      setConnectionStatus('error');
      addMessage('error', `Connection failed: ${error.message}`);
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (wsRef.current) {
      wsRef.current.close();
      stopPingTest();
    }
  };

  // Send message
  const sendMessage = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN && messageInput.trim()) {
      try {
        const message = {
          type: 'test_message',
          content: messageInput.trim(),
          timestamp: new Date().toISOString(),
          sender: 'test_client'
        };
        
        wsRef.current.send(JSON.stringify(message));
        addMessage('sent', JSON.stringify(message, null, 2));
        setMessageInput('');
      } catch (error) {
        addMessage('error', `Failed to send message: ${error.message}`);
      }
    }
  };

  // Send ping
  const sendPing = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const pingTime = Date.now();
      setLastPing(pingTime);
      
      const pingMessage = {
        type: 'ping',
        timestamp: pingTime
      };
      
      wsRef.current.send(JSON.stringify(pingMessage));
      addMessage('sent', 'Ping sent');
    }
  };

  // Start automatic ping test
  const startPingTest = () => {
    pingIntervalRef.current = setInterval(() => {
      sendPing();
    }, 10000); // Ping every 10 seconds
  };

  // Stop ping test
  const stopPingTest = () => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  };

  // Add message to list
  const addMessage = (type, content) => {
    const newMessage = {
      id: Date.now() + Math.random(),
      type,
      content,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };

  // Get status color
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'processing';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return <CheckCircleOutlined />;
      case 'connecting': return <SyncOutlined spin />;
      case 'error': return <CloseCircleOutlined />;
      default: return <DisconnectOutlined />;
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={1}>WebSocket Real-time Test</Title>
      <Text type="secondary">
        Testing WebSocket connectivity and real-time communication features
      </Text>

      <Divider />

      {/* Connection Status */}
      <Card title="Connection Status" size="small" style={{ marginBottom: 16 }}>
        <Space size="large">
          <div>
            <Text strong>Status: </Text>
            <Tag color={getStatusColor()} icon={getStatusIcon()}>
              {connectionStatus.toUpperCase()}
            </Tag>
          </div>
          <div>
            <Text strong>URL: </Text>
            <Input
              value={wsUrl}
              onChange={(e) => setWsUrl(e.target.value)}
              style={{ width: 300 }}
              disabled={connectionStatus === 'connected'}
            />
          </div>
          {latency && (
            <div>
              <Text strong>Latency: </Text>
              <Tag color="blue">{latency}ms</Tag>
            </div>
          )}
        </Space>
      </Card>

      {/* Controls */}
      <Card title="Controls" size="small" style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            onClick={connect}
            disabled={connectionStatus === 'connected' || connectionStatus === 'connecting'}
          >
            Connect
          </Button>
          <Button
            onClick={disconnect}
            disabled={connectionStatus === 'disconnected'}
            icon={<DisconnectOutlined />}
          >
            Disconnect
          </Button>
          <Button
            onClick={sendPing}
            disabled={connectionStatus !== 'connected'}
            icon={<SyncOutlined />}
          >
            Send Ping
          </Button>
          <Button
            onClick={clearMessages}
            icon={<ReloadOutlined />}
          >
            Clear Messages
          </Button>
        </Space>
      </Card>

      {/* Message Sender */}
      <Card title="Send Message" size="small" style={{ marginBottom: 16 }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            placeholder="Enter your message (JSON format supported)"
            rows={2}
            onPressEnter={(e) => {
              if (e.ctrlKey || e.metaKey) {
                sendMessage();
              }
            }}
          />
          <Button
            type="primary"
            onClick={sendMessage}
            disabled={connectionStatus !== 'connected' || !messageInput.trim()}
            icon={<SendOutlined />}
          >
            Send
          </Button>
        </Space.Compact>
        <Text type="secondary" style={{ fontSize: 12 }}>
          Press Ctrl+Enter to send quickly
        </Text>
      </Card>

      {/* Messages */}
      <Card title={`Messages (${messages.length})`} size="small">
        <List
          dataSource={messages}
          renderItem={(message) => (
            <List.Item>
              <div style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Tag color={
                    message.type === 'system' ? 'blue' :
                    message.type === 'sent' ? 'green' :
                    message.type === 'received' ? 'orange' : 'red'
                  }>
                    {message.type.toUpperCase()}
                  </Tag>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {message.timestamp}
                  </Text>
                </div>
                <pre style={{ 
                  margin: 0, 
                  whiteSpace: 'pre-wrap', 
                  fontSize: 12,
                  background: '#f5f5f5',
                  padding: 8,
                  borderRadius: 4
                }}>
                  {message.content}
                </pre>
              </div>
            </List.Item>
          )}
          style={{ maxHeight: 400, overflow: 'auto' }}
        />
      </Card>

      {/* Test Results */}
      <Alert
        message="WebSocket Test Results"
        description={
          <Space direction="vertical">
            <Text>✅ WebSocket connection: {connectionStatus === 'connected' ? 'Working' : 'Not connected'}</Text>
            <Text>✅ Message sending: {connectionStatus === 'connected' ? 'Available' : 'Requires connection'}</Text>
            <Text>✅ Message receiving: {messages.filter(m => m.type === 'received').length > 0 ? 'Working' : 'No messages received yet'}</Text>
            <Text>✅ Ping/Pong latency: {latency ? `${latency}ms` : 'Not measured yet'}</Text>
            <Text>✅ Real-time communication: {connectionStatus === 'connected' ? 'Ready' : 'Requires connection'}</Text>
          </Space>
        }
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </div>
  );
};

export default WebSocketRealTimeTest;
