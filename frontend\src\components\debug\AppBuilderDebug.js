/**
 * Debug Component for App Builder
 * 
 * This component helps diagnose issues with the App Builder loading
 */

import React, { useState, useEffect } from 'react';
import { Card, Alert, Button, Typography, Space, Divider, Tag } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  LoadingOutlined,
  BugOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

export default function AppBuilderDebug() {
  const [componentTests, setComponentTests] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runTests = async () => {
      const tests = {};
      
      // Test 1: Check if IntegratedAppBuilder exists
      try {
        const IntegratedAppBuilder = await import('../builder/IntegratedAppBuilder');
        tests.integratedAppBuilder = {
          status: 'success',
          message: 'IntegratedAppBuilder component found and loadable',
          component: IntegratedAppBuilder.default
        };
      } catch (error) {
        tests.integratedAppBuilder = {
          status: 'error',
          message: `IntegratedAppBuilder failed to load: ${error.message}`,
          error: error
        };
      }

      // Test 2: Check if MinimalIntegratedAppBuilder exists
      try {
        const MinimalIntegratedAppBuilder = await import('../builder/MinimalIntegratedAppBuilder');
        tests.minimalIntegratedAppBuilder = {
          status: 'success',
          message: 'MinimalIntegratedAppBuilder component found and loadable',
          component: MinimalIntegratedAppBuilder.default
        };
      } catch (error) {
        tests.minimalIntegratedAppBuilder = {
          status: 'error',
          message: `MinimalIntegratedAppBuilder failed to load: ${error.message}`,
          error: error
        };
      }

      // Test 3: Check if SimpleAppBuilder exists
      try {
        const SimpleAppBuilder = await import('../builder/SimpleAppBuilder');
        tests.simpleAppBuilder = {
          status: 'success',
          message: 'SimpleAppBuilder component found and loadable',
          component: SimpleAppBuilder.default
        };
      } catch (error) {
        tests.simpleAppBuilder = {
          status: 'error',
          message: `SimpleAppBuilder failed to load: ${error.message}`,
          error: error
        };
      }

      // Test 4: Check React and dependencies
      tests.react = {
        status: 'success',
        message: `React version: ${React.version}`,
        details: {
          reactVersion: React.version,
          hasReactDOM: !!window.ReactDOM,
          hasAntd: !!window.antd
        }
      };

      // Test 5: Check browser environment
      tests.browser = {
        status: 'success',
        message: 'Browser environment check',
        details: {
          userAgent: navigator.userAgent,
          localStorage: !!window.localStorage,
          sessionStorage: !!window.sessionStorage,
          fetch: !!window.fetch,
          Promise: !!window.Promise
        }
      };

      setComponentTests(tests);
      setLoading(false);
    };

    runTests();
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'processing';
    }
  };

  if (loading) {
    return (
      <Card style={{ margin: '20px', textAlign: 'center' }}>
        <LoadingOutlined style={{ fontSize: '24px', marginBottom: '16px' }} />
        <Title level={4}>Running Diagnostics...</Title>
        <Text type="secondary">Checking App Builder components and dependencies</Text>
      </Card>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>
          <BugOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          App Builder Diagnostics
        </Title>
        
        <Alert
          message="Diagnostic Results"
          description="This page shows the status of all App Builder components and helps identify loading issues."
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {Object.entries(componentTests).map(([testName, result]) => (
            <Card key={testName} size="small">
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Space>
                  {getStatusIcon(result.status)}
                  <Text strong style={{ textTransform: 'capitalize' }}>
                    {testName.replace(/([A-Z])/g, ' $1').trim()}
                  </Text>
                </Space>
                <Tag color={getStatusColor(result.status)}>
                  {result.status.toUpperCase()}
                </Tag>
              </Space>
              
              <Paragraph style={{ marginTop: '8px', marginBottom: '0' }}>
                {result.message}
              </Paragraph>

              {result.error && (
                <Alert
                  message="Error Details"
                  description={result.error.stack || result.error.message}
                  type="error"
                  size="small"
                  style={{ marginTop: '8px' }}
                />
              )}

              {result.details && (
                <div style={{ marginTop: '8px' }}>
                  <Text strong>Details:</Text>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginTop: '4px'
                  }}>
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </div>
              )}
            </Card>
          ))}
        </Space>

        <Divider />

        <Title level={4}>
          <InfoCircleOutlined style={{ marginRight: '8px' }} />
          Troubleshooting Steps
        </Title>

        <ol>
          <li>
            <Text strong>If IntegratedAppBuilder fails to load:</Text>
            <ul>
              <li>Check the browser console for detailed error messages</li>
              <li>Verify all dependencies are installed correctly</li>
              <li>Try refreshing the page</li>
            </ul>
          </li>
          <li>
            <Text strong>If MinimalIntegratedAppBuilder loads successfully:</Text>
            <ul>
              <li>The basic import system is working</li>
              <li>The issue is likely in the full IntegratedAppBuilder component</li>
              <li>Check for missing dependencies or syntax errors</li>
            </ul>
          </li>
          <li>
            <Text strong>If SimpleAppBuilder loads successfully:</Text>
            <ul>
              <li>You can use the basic app builder functionality</li>
              <li>The enhanced features may need additional setup</li>
            </ul>
          </li>
        </ol>

        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Space>
            <Button type="primary" onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
            <Button onClick={() => window.location.href = '/'}>
              Go to Home
            </Button>
            <Button 
              type="dashed" 
              onClick={() => console.log('Component Tests:', componentTests)}
            >
              Log Results to Console
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
}
