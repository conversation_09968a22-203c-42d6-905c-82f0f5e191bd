/**
 * App Builder Actions
 * 
 * Unified actions for App Builder features integration
 * Handles components, layouts, themes, and export functionality
 */

import * as types from './types';

// Component Actions
export const addComponent = (component) => ({
  type: types.ADD_COMPONENT,
  payload: {
    ...component,
    id: component.id || `component_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
});

export const updateComponent = (componentId, updates) => ({
  type: types.UPDATE_COMPONENT,
  payload: {
    id: componentId,
    ...updates,
    updatedAt: new Date().toISOString()
  }
});

export const deleteComponent = (componentId) => ({
  type: types.DELETE_COMPONENT,
  payload: { id: componentId }
});

export const duplicateComponent = (componentId) => ({
  type: types.DUPLICATE_COMPONENT,
  payload: { id: componentId }
});

// Layout Actions
export const addLayout = (layout) => ({
  type: types.ADD_LAYOUT,
  payload: {
    ...layout,
    id: layout.id || `layout_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
});

export const updateLayout = (layoutId, updates) => ({
  type: types.UPDATE_LAYOUT,
  payload: {
    id: layoutId,
    ...updates,
    updatedAt: new Date().toISOString()
  }
});

export const deleteLayout = (layoutId) => ({
  type: types.DELETE_LAYOUT,
  payload: { id: layoutId }
});

export const addComponentToLayout = (layoutId, componentId, position) => ({
  type: types.ADD_COMPONENT_TO_LAYOUT,
  payload: {
    layoutId,
    componentId,
    position: {
      x: position?.x || 0,
      y: position?.y || 0,
      width: position?.width || 1,
      height: position?.height || 1
    },
    id: `layout_item_${Date.now()}`
  }
});

export const removeComponentFromLayout = (layoutId, layoutItemId) => ({
  type: types.REMOVE_COMPONENT_FROM_LAYOUT,
  payload: {
    layoutId,
    layoutItemId
  }
});

export const moveComponentInLayout = (layoutId, layoutItemId, newPosition) => ({
  type: types.MOVE_COMPONENT_IN_LAYOUT,
  payload: {
    layoutId,
    layoutItemId,
    position: newPosition
  }
});

// Theme Actions
export const addTheme = (theme) => ({
  type: types.ADD_THEME,
  payload: {
    ...theme,
    id: theme.id || `theme_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
});

export const updateTheme = (themeId, updates) => ({
  type: types.UPDATE_THEME,
  payload: {
    id: themeId,
    ...updates,
    updatedAt: new Date().toISOString()
  }
});

export const deleteTheme = (themeId) => ({
  type: types.DELETE_THEME,
  payload: { id: themeId }
});

export const setActiveTheme = (themeId) => ({
  type: types.SET_ACTIVE_THEME,
  payload: { themeId }
});

export const applyThemeToComponent = (componentId, themeId) => ({
  type: types.APPLY_THEME_TO_COMPONENT,
  payload: { componentId, themeId }
});

export const applyThemeToLayout = (layoutId, themeId) => ({
  type: types.APPLY_THEME_TO_LAYOUT,
  payload: { layoutId, themeId }
});

// Project Actions
export const createProject = (projectData) => ({
  type: types.CREATE_PROJECT,
  payload: {
    ...projectData,
    id: projectData.id || `project_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    components: [],
    layouts: [],
    themes: [],
    activeTheme: null
  }
});

export const updateProject = (projectId, updates) => ({
  type: types.UPDATE_PROJECT,
  payload: {
    id: projectId,
    ...updates,
    updatedAt: new Date().toISOString()
  }
});

export const setActiveProject = (projectId) => ({
  type: types.SET_ACTIVE_PROJECT,
  payload: { projectId }
});

// Export Actions
export const setExportSettings = (settings) => ({
  type: types.SET_EXPORT_SETTINGS,
  payload: settings
});

export const exportProject = (projectId, format, options = {}) => ({
  type: types.EXPORT_PROJECT,
  payload: {
    projectId,
    format,
    options,
    timestamp: new Date().toISOString()
  }
});

export const exportProjectSuccess = (exportData) => ({
  type: types.EXPORT_PROJECT_SUCCESS,
  payload: exportData
});

export const exportProjectFailure = (error) => ({
  type: types.EXPORT_PROJECT_FAILURE,
  payload: { error }
});

// UI State Actions
export const setActiveFeature = (feature) => ({
  type: types.SET_ACTIVE_FEATURE,
  payload: { feature }
});

export const setSelectedComponent = (componentId) => ({
  type: types.SET_SELECTED_COMPONENT,
  payload: { componentId }
});

export const setSelectedLayout = (layoutId) => ({
  type: types.SET_SELECTED_LAYOUT,
  payload: { layoutId }
});

export const setPreviewMode = (enabled) => ({
  type: types.SET_PREVIEW_MODE,
  payload: { enabled }
});

export const setDraggedComponent = (component) => ({
  type: types.SET_DRAGGED_COMPONENT,
  payload: { component }
});

export const clearDraggedComponent = () => ({
  type: types.CLEAR_DRAGGED_COMPONENT
});

// Undo/Redo Actions
export const addToHistory = (action) => ({
  type: types.ADD_TO_HISTORY,
  payload: { action }
});

export const undo = () => ({
  type: types.UNDO
});

export const redo = () => ({
  type: types.REDO
});

export const clearHistory = () => ({
  type: types.CLEAR_HISTORY
});
