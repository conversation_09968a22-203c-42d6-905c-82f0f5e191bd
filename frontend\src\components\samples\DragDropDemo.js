/**
 * Drag and Drop Demonstration Component
 * 
 * This component provides a practical demonstration of the drag-and-drop
 * functionality with visual feedback and step-by-step guidance.
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Typography, 
  Space, 
  Alert, 
  Steps,
  Tag,
  message,
  Tooltip,
  Progress
} from 'antd';
import {
  DragOutlined,
  DropboxOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  BulbOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled components for the demo
const DemoContainer = styled.div`
  display: flex;
  gap: 24px;
  min-height: 500px;
  padding: 24px;
  background: #f5f5f5;
  border-radius: 8px;
`;

const PaletteArea = styled.div`
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  
  &.highlight {
    border-color: #1890ff;
    background: #f0f8ff;
  }
`;

const CanvasArea = styled.div`
  flex: 2;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  position: relative;
  min-height: 400px;
  
  &.drag-over {
    border-color: #52c41a;
    background: #f6ffed;
  }
  
  &.drop-success {
    border-color: #52c41a;
    background: #f6ffed;
    animation: pulse 0.5s ease-in-out;
  }
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
  }
`;

const DraggableComponent = styled.div`
  padding: 12px;
  margin: 8px 0;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    cursor: grabbing;
    transform: scale(0.95);
  }
  
  &.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }
`;

const DroppedComponent = styled.div`
  position: absolute;
  padding: 8px 12px;
  background: #fff;
  border: 2px solid #52c41a;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: dropIn 0.3s ease-out;
  
  @keyframes dropIn {
    0% {
      opacity: 0;
      transform: scale(0.8) translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
`;

// Demo components data
const DEMO_COMPONENTS = [
  { id: 'text', label: 'Text Component', icon: '📝', color: '#1890ff' },
  { id: 'button', label: 'Button Component', icon: '🔘', color: '#52c41a' },
  { id: 'card', label: 'Card Component', icon: '🃏', color: '#fa8c16' },
  { id: 'header', label: 'Header Component', icon: '📋', color: '#722ed1' }
];

// Demo steps
const DEMO_STEPS = [
  { title: 'Select Component', description: 'Choose a component from the palette' },
  { title: 'Start Dragging', description: 'Click and hold to start dragging' },
  { title: 'Drop on Canvas', description: 'Release over the canvas area' },
  { title: 'Success!', description: 'Component added to your application' }
];

export default function DragDropDemo({ onComponentAdd, showTutorial = true }) {
  const [currentStep, setCurrentStep] = useState(0);
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [droppedComponents, setDroppedComponents] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [demoProgress, setDemoProgress] = useState(0);
  const canvasRef = useRef(null);

  // Update progress based on dropped components
  useEffect(() => {
    const progress = (droppedComponents.length / DEMO_COMPONENTS.length) * 100;
    setDemoProgress(progress);
  }, [droppedComponents]);

  // Handle drag start
  const handleDragStart = useCallback((e, component) => {
    setDraggedComponent(component);
    setCurrentStep(1);
    
    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.id,
      label: component.label,
      source: 'demo-palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';
    
    message.info(`Started dragging ${component.label}`);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setDraggedComponent(null);
    setCurrentStep(0);
  }, []);

  // Handle drag over canvas
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    setIsDragOver(true);
    setCurrentStep(2);
  }, []);

  // Handle drag leave canvas
  const handleDragLeave = useCallback((e) => {
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      setCurrentStep(1);
    }
  }, []);

  // Handle drop on canvas
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    try {
      const dragData = e.dataTransfer.getData('application/json');
      if (dragData) {
        const componentData = JSON.parse(dragData);
        
        // Calculate drop position
        const rect = canvasRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Add component to canvas
        const newComponent = {
          id: `${componentData.type}-${Date.now()}`,
          type: componentData.type,
          label: componentData.label,
          position: { x, y }
        };
        
        setDroppedComponents(prev => [...prev, newComponent]);
        setCurrentStep(3);
        setShowSuccess(true);
        
        // Show success feedback
        message.success(`${componentData.label} added to canvas!`);
        
        // Call parent callback
        if (onComponentAdd) {
          onComponentAdd(componentData.type, { x, y });
        }
        
        // Reset success state
        setTimeout(() => {
          setShowSuccess(false);
          setCurrentStep(0);
        }, 2000);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
      message.error('Failed to add component');
    }
  }, [onComponentAdd]);

  // Reset demo
  const handleReset = useCallback(() => {
    setDroppedComponents([]);
    setCurrentStep(0);
    setDraggedComponent(null);
    setIsDragOver(false);
    setShowSuccess(false);
    setDemoProgress(0);
    message.info('Demo reset');
  }, []);

  // Auto-demo function
  const handleAutoDemo = useCallback(() => {
    message.info('Starting automatic demo...');
    
    DEMO_COMPONENTS.forEach((component, index) => {
      setTimeout(() => {
        const newComponent = {
          id: `${component.id}-auto-${Date.now()}`,
          type: component.id,
          label: component.label,
          position: { 
            x: 50 + (index * 120), 
            y: 50 + (index * 80) 
          }
        };
        
        setDroppedComponents(prev => [...prev, newComponent]);
        
        if (onComponentAdd) {
          onComponentAdd(component.id, newComponent.position);
        }
        
        if (index === DEMO_COMPONENTS.length - 1) {
          message.success('Auto demo completed!');
        }
      }, index * 1000);
    });
  }, [onComponentAdd]);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={3}>
        <DragOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        Drag & Drop Demonstration
      </Title>
      
      <Paragraph>
        This interactive demo shows how to use the drag-and-drop functionality. 
        Drag components from the palette on the left to the canvas on the right.
      </Paragraph>

      {/* Tutorial steps */}
      {showTutorial && (
        <Card style={{ marginBottom: '24px' }}>
          <Steps current={currentStep} size="small">
            {DEMO_STEPS.map((step, index) => (
              <Step key={index} title={step.title} description={step.description} />
            ))}
          </Steps>
        </Card>
      )}

      {/* Progress indicator */}
      <Card style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>Demo Progress</Text>
            <Space>
              <Button size="small" icon={<PlayCircleOutlined />} onClick={handleAutoDemo}>
                Auto Demo
              </Button>
              <Button size="small" icon={<ReloadOutlined />} onClick={handleReset}>
                Reset
              </Button>
            </Space>
          </div>
          <Progress 
            percent={demoProgress} 
            status={demoProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <Text type="secondary">
            {droppedComponents.length} of {DEMO_COMPONENTS.length} components added
          </Text>
        </Space>
      </Card>

      {/* Main demo area */}
      <DemoContainer>
        {/* Component Palette */}
        <PaletteArea className={draggedComponent ? 'highlight' : ''}>
          <Title level={5}>
            <BulbOutlined style={{ marginRight: '8px' }} />
            Component Palette
          </Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            Drag these components to the canvas
          </Text>
          
          {DEMO_COMPONENTS.map((component) => (
            <Tooltip key={component.id} title={`Drag ${component.label} to canvas`}>
              <DraggableComponent
                draggable
                className={draggedComponent?.id === component.id ? 'dragging' : ''}
                onDragStart={(e) => handleDragStart(e, component)}
                onDragEnd={handleDragEnd}
              >
                <Space>
                  <span style={{ fontSize: '20px' }}>{component.icon}</span>
                  <div>
                    <Text strong>{component.label}</Text>
                    <br />
                    <Tag color={component.color} size="small">{component.id}</Tag>
                  </div>
                  <DragOutlined style={{ color: '#999' }} />
                </Space>
              </DraggableComponent>
            </Tooltip>
          ))}
        </PaletteArea>

        {/* Canvas Area */}
        <CanvasArea
          ref={canvasRef}
          className={`${isDragOver ? 'drag-over' : ''} ${showSuccess ? 'drop-success' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Title level={5}>
            <DropboxOutlined style={{ marginRight: '8px' }} />
            Canvas Area
          </Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            Drop components here to build your application
          </Text>

          {/* Drop zone indicator */}
          {droppedComponents.length === 0 && !isDragOver && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: '#999'
            }}>
              <DropboxOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <br />
              <Text type="secondary">Drop components here</Text>
            </div>
          )}

          {/* Drag over indicator */}
          {isDragOver && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: '#52c41a'
            }}>
              <DropboxOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <br />
              <Text strong style={{ color: '#52c41a' }}>Release to drop here</Text>
            </div>
          )}

          {/* Dropped components */}
          {droppedComponents.map((component) => (
            <DroppedComponent
              key={component.id}
              style={{
                left: component.position.x,
                top: component.position.y
              }}
            >
              <Space>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <Text strong>{component.label}</Text>
              </Space>
            </DroppedComponent>
          ))}
        </CanvasArea>
      </DemoContainer>

      {/* Success message */}
      {demoProgress === 100 && (
        <Alert
          message="Demo Completed!"
          description="You've successfully demonstrated all drag-and-drop functionality. The App Builder is ready for use."
          type="success"
          showIcon
          style={{ marginTop: '24px' }}
        />
      )}
    </div>
  );
}
