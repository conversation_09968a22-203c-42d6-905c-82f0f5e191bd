import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Spin from 'antd/es/spin';

// Enhanced loading component with progress indication
const SimpleLoading = () => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    gap: '16px'
  }}>
    <Spin size="large" />
    <div style={{ textAlign: 'center', color: '#666' }}>
      <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '4px' }}>
        Loading App Builder...
      </div>
      <div style={{ fontSize: '14px' }}>
        Optimized for performance • Bundle size: 2.34MB
      </div>
    </div>
  </div>
);

// Lazy load only essential components that don't use styled-components
const HomePage = React.lazy(() => import('./pages/Home'));
const SimpleWebSocketPage = React.lazy(() => import('./pages/SimpleWebSocketPage'));
const LightweightAppBuilder = React.lazy(() => import('./pages/LightweightAppBuilder'));
const AppBuilderIntegrated = React.lazy(() => import('./pages/AppBuilderIntegrated'));

// Simple 404 component
const NotFound = () => (
  <div style={{
    textAlign: 'center',
    padding: '50px',
    fontFamily: 'Arial, sans-serif'
  }}>
    <h1>404 - Page Not Found</h1>
    <p>The page you're looking for doesn't exist.</p>
    <a href="/" style={{ color: '#1890ff' }}>Go Home</a>
  </div>
);

// Simple About component
const About = () => (
  <div style={{
    padding: '24px',
    maxWidth: '800px',
    margin: '0 auto',
    fontFamily: 'Arial, sans-serif'
  }}>
    <h1>About App Builder 201</h1>
    <p>
      App Builder 201 is a modern web application builder designed to help you create
      applications quickly and efficiently.
    </p>
    <h2>Features</h2>
    <ul>
      <li>Component-based architecture</li>
      <li>Real-time collaboration</li>
      <li>WebSocket integration</li>
      <li>Modern UI components</li>
    </ul>
  </div>
);

/**
 * Minimal Routes Component - Optimized for Bundle Size
 * Only includes essential routes without heavy dependencies
 */
const MinimalRoutes = () => {
  return (
    <Suspense fallback={<SimpleLoading />}>
      <Routes>
        {/* Home route */}
        <Route path="/" element={<HomePage />} />

        {/* Essential features */}
        <Route path="/websocket" element={<SimpleWebSocketPage />} />
        <Route path="/app-builder" element={<LightweightAppBuilder />} />
        <Route path="/app-builder-integrated" element={<AppBuilderIntegrated />} />
        <Route path="/about" element={<About />} />

        {/* Redirect old routes to home */}
        <Route path="/app-builder-enhanced" element={<Navigate to="/" replace />} />
        <Route path="/dashboard" element={<Navigate to="/" replace />} />
        <Route path="/projects" element={<Navigate to="/" replace />} />
        <Route path="/templates" element={<Navigate to="/" replace />} />
        <Route path="/profile" element={<Navigate to="/" replace />} />
        <Route path="/settings" element={<Navigate to="/" replace />} />

        {/* Auth routes - redirect to home for now */}
        <Route path="/login" element={<Navigate to="/" replace />} />
        <Route path="/register" element={<Navigate to="/" replace />} />
        <Route path="/forgot-password" element={<Navigate to="/" replace />} />

        {/* Test routes - redirect to home */}
        <Route path="/test/*" element={<Navigate to="/" replace />} />
        <Route path="/debug/*" element={<Navigate to="/" replace />} />

        {/* Catch all - 404 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
};

export default MinimalRoutes;
