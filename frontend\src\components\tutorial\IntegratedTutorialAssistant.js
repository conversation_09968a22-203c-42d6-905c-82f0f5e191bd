/**
 * Integrated Tutorial Assistant Component
 * 
 * Enhanced tutorial system specifically designed for the App Builder with
 * interactive overlays, context-aware help, and seamless integration.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Progress,
  Badge,
  Tooltip,
  Modal,
  Drawer,
  List,
  Tag,
  Avatar,
  Divider,
  Row,
  Col,
  Switch,
  notification,
  FloatButton,
  Steps
} from 'antd';
import {
  BookOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  StepBackwardOutlined,
  CloseOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  BulbOutlined,
  RocketOutlined,
  EyeOutlined,
  AppstoreOutlined,
  LayoutOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Import tutorial settings panel
let TutorialSettingsPanel;
try {
  TutorialSettingsPanel = require('./TutorialSettingsPanel').default;
} catch (error) {
  console.warn('TutorialSettingsPanel not available');
  TutorialSettingsPanel = () => <div>Settings panel not available</div>;
}

// Styled Components
const TutorialContainer = styled.div`
  position: relative;
`;

const TutorialCard = styled(Card)`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  z-index: 1000;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  border: none;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
  }
  
  .ant-card-head-title {
    color: white;
    font-weight: 600;
  }
  
  .ant-card-body {
    padding: 20px;
  }
`;

const ProgressContainer = styled.div`
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
`;

const StepIndicator = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const ContextualHint = styled.div`
  position: absolute;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  z-index: 999;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  animation: fadeInBounce 0.5s ease-out;
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -6px;
    border-width: 6px;
    border-style: solid;
    border-color: #1890ff transparent transparent transparent;
  }
  
  @keyframes fadeInBounce {
    0% { opacity: 0; transform: translateY(-10px) scale(0.8); }
    100% { opacity: 1; transform: translateY(0) scale(1); }
  }
`;

const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 998;
  pointer-events: ${props => props.allowInteraction ? 'none' : 'auto'};
  backdrop-filter: blur(2px);
`;

const Spotlight = styled.div`
  position: absolute;
  border: 3px solid #1890ff;
  border-radius: 12px;
  box-shadow: 
    0 0 0 4px rgba(24, 144, 255, 0.3),
    0 0 0 9999px rgba(0, 0, 0, 0.6);
  pointer-events: none;
  z-index: 999;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    50% { 
      box-shadow: 
        0 0 0 8px rgba(24, 144, 255, 0.2),
        0 0 0 16px rgba(24, 144, 255, 0.05),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    100% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
  }
`;

// Enhanced App Builder specific tutorial steps with comprehensive workflow coverage
const APP_BUILDER_TUTORIALS = {
  getting_started: {
    id: 'getting_started',
    title: 'Getting Started with App Builder',
    description: 'Learn the basics of creating applications',
    category: 'beginner',
    estimatedDuration: 8,
    prerequisites: [],
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to App Builder! 🎉',
        content: 'App Builder lets you create applications visually using drag-and-drop components. This interactive tutorial will guide you through creating your first app from start to finish.',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['next'],
        validation: null
      },
      {
        id: 'interface-overview',
        title: 'Interface Overview',
        content: 'Let\'s explore the main areas of the App Builder interface. Each area has a specific purpose in your app creation workflow.',
        target: '[data-tutorial="main-interface"]',
        position: 'center',
        type: 'highlight',
        actions: ['next', 'previous'],
        validation: null
      },
      {
        id: 'component-palette',
        title: 'Component Palette 🎨',
        content: 'This is where you\'ll find all available components. Drag components from here to the canvas to start building your app. Try hovering over different components to see what they do.',
        target: '[data-tutorial="component-palette"]',
        position: 'right',
        type: 'spotlight',
        actions: ['next', 'previous'],
        validation: {
          type: 'hover',
          target: '[data-tutorial="component-palette"] .component-item',
          message: 'Great! You\'re exploring the components.'
        }
      },
      {
        id: 'first-component',
        title: 'Add Your First Component',
        content: 'Now let\'s add your first component! Drag a Button component from the palette to the canvas area. Don\'t worry, you can always move or delete it later.',
        target: '[data-tutorial="canvas-area"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous', 'skip'],
        validation: {
          type: 'component-added',
          componentType: 'button',
          message: 'Excellent! You\'ve added your first component.'
        }
      },
      {
        id: 'property-editor',
        title: 'Customize Properties ⚙️',
        content: 'When you select a component, you can customize its properties here. Try clicking on your button and changing its text or color.',
        target: '[data-tutorial="property-editor"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'property-changed',
          message: 'Perfect! You\'re customizing your component.'
        }
      },
      {
        id: 'preview-mode',
        title: 'Preview Your App 👁️',
        content: 'Click the preview button to see how your app will look to users. You can test interactions and see your app in action.',
        target: '[data-tutorial="preview-mode"]',
        position: 'bottom',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'preview-activated',
          message: 'Great! You\'re previewing your app.'
        }
      },
      {
        id: 'save-project',
        title: 'Save Your Work 💾',
        content: 'Don\'t forget to save your progress! Your work is automatically saved, but you can also manually save at any time.',
        target: '[data-tutorial="save-button"]',
        position: 'top',
        type: 'highlight',
        actions: ['next', 'previous'],
        validation: null
      },
      {
        id: 'completion',
        title: 'Congratulations! 🎊',
        content: 'You\'ve completed the getting started tutorial! You now know the basics of creating apps with App Builder. Ready to explore more advanced features?',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'explore-advanced'],
        validation: null
      }
    ]
  },

  complete_workflow: {
    id: 'complete_workflow',
    title: 'Complete App Creation Workflow',
    description: 'Learn the complete process from template to deployment',
    category: 'intermediate',
    estimatedDuration: 15,
    prerequisites: ['getting_started'],
    steps: [
      {
        id: 'template-selection',
        title: 'Choose a Template',
        content: 'Start by selecting a template that matches your app idea. Templates provide a solid foundation and save you time.',
        target: '[data-tutorial="template-selector"]',
        position: 'center',
        type: 'interactive',
        actions: ['next', 'skip'],
        validation: {
          type: 'template-selected',
          message: 'Great choice! Your template is loaded.'
        }
      },
      {
        id: 'layout-design',
        title: 'Design Your Layout',
        content: 'Use the Layout Designer to create responsive layouts. Arrange your components in grids, flexbox, or custom layouts.',
        target: '[data-tutorial="layout-designer"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'layout-modified',
          message: 'Excellent layout design!'
        }
      },
      {
        id: 'theme-application',
        title: 'Apply Themes',
        content: 'Customize the look and feel of your app with the Theme Manager. Choose colors, fonts, and styling that match your brand.',
        target: '[data-tutorial="theme-manager"]',
        position: 'left',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'theme-applied',
          message: 'Beautiful! Your app has a great look now.'
        }
      },
      {
        id: 'component-customization',
        title: 'Advanced Component Setup',
        content: 'Add more components and customize their behavior. Set up interactions, data binding, and advanced properties.',
        target: '[data-tutorial="component-palette"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'advanced-component-added',
          message: 'You\'re building a sophisticated app!'
        }
      },
      {
        id: 'testing-validation',
        title: 'Test Your App',
        content: 'Use the Testing Tools to validate your app works correctly. Test different screen sizes, interactions, and edge cases.',
        target: '[data-tutorial="testing-tools"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'test-run',
          message: 'Great! Your app is well-tested.'
        }
      },
      {
        id: 'performance-check',
        title: 'Performance Optimization',
        content: 'Check your app\'s performance metrics and apply optimization suggestions for the best user experience.',
        target: '[data-tutorial="performance-monitor"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'performance-checked',
          message: 'Your app is optimized for performance!'
        }
      },
      {
        id: 'code-export',
        title: 'Export Your Code',
        content: 'Finally, export your app to your preferred framework. Choose from React, Vue, Angular, or others with full TypeScript support.',
        target: '[data-tutorial="code-export"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'code-exported',
          message: 'Perfect! Your app is ready for deployment.'
        }
      },
      {
        id: 'workflow-complete',
        title: 'Workflow Complete! 🚀',
        content: 'Congratulations! You\'ve mastered the complete app creation workflow. You can now build professional applications from start to finish.',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'start-new-project'],
        validation: null
      }
    ]
  },

  advanced_features: {
    id: 'advanced_features',
    title: 'Advanced Features & Collaboration',
    description: 'Master advanced features and real-time collaboration',
    category: 'advanced',
    estimatedDuration: 12,
    prerequisites: ['getting_started', 'complete_workflow'],
    steps: [
      {
        id: 'websocket-collaboration',
        title: 'Real-time Collaboration',
        content: 'Learn how to collaborate with team members in real-time. See live cursors, changes, and comments as you work together.',
        target: '[data-tutorial="websocket-manager"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'collaboration-enabled',
          message: 'You\'re now collaborating in real-time!'
        }
      },
      {
        id: 'data-management',
        title: 'Advanced Data Management',
        content: 'Set up complex data flows, state management, and API integrations for dynamic applications.',
        target: '[data-tutorial="data-management"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'data-binding-setup',
          message: 'Excellent data management setup!'
        }
      },
      {
        id: 'custom-components',
        title: 'Create Custom Components',
        content: 'Build your own reusable components and add them to your component library for future projects.',
        target: '[data-tutorial="component-builder"]',
        position: 'right',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'custom-component-created',
          message: 'You\'ve created a custom component!'
        }
      },
      {
        id: 'advanced-testing',
        title: 'Comprehensive Testing',
        content: 'Set up automated tests, accessibility checks, and cross-browser validation for production-ready apps.',
        target: '[data-tutorial="testing-tools"]',
        position: 'top',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'advanced-test-setup',
          message: 'Your testing suite is comprehensive!'
        }
      },
      {
        id: 'deployment-ready',
        title: 'Production Deployment',
        content: 'Prepare your app for production with optimization, security checks, and deployment configurations.',
        target: '[data-tutorial="deployment-tools"]',
        position: 'center',
        type: 'interactive',
        actions: ['next', 'previous'],
        validation: {
          type: 'deployment-configured',
          message: 'Your app is deployment-ready!'
        }
      },
      {
        id: 'mastery-complete',
        title: 'App Builder Master! 🏆',
        content: 'You\'ve mastered all advanced features of App Builder! You can now create professional, collaborative, and production-ready applications.',
        target: null,
        position: 'center',
        type: 'modal',
        actions: ['complete', 'share-achievement'],
        validation: null
      }
    ]
  }
};

/**
 * IntegratedTutorialAssistant Component
 */
const IntegratedTutorialAssistant = ({
  enableAutoStart = false,
  showContextualHelp = true,
  onTutorialComplete,
  onTutorialSkip,
  features = []
}) => {
  // State
  const [isActive, setIsActive] = useState(false);
  const [currentTutorial, setCurrentTutorial] = useState(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [showTutorialList, setShowTutorialList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [contextualHints, setContextualHints] = useState([]);
  const [spotlightPosition, setSpotlightPosition] = useState(null);
  const [completedTutorials, setCompletedTutorials] = useState(new Set());
  const [assistantSettings, setAssistantSettings] = useState({
    autoAdvance: false,
    showHints: true,
    playSound: false,
    animationSpeed: 'normal'
  });
  const [tutorialSettings, setTutorialSettings] = useState({
    enabled: true,
    showContextualHelp: true,
    showHints: true,
    autoAdvance: false,
    playSound: false,
    animationSpeed: 'normal',
    helpDelay: 2000,
    autoStartForNewUsers: true,
    showProgressNotifications: true,
    enableKeyboardShortcuts: true
  });

  // Get current tutorial step
  const currentStep = useMemo(() => {
    if (!currentTutorial || currentStepIndex >= currentTutorial.steps.length) return null;
    return currentTutorial.steps[currentStepIndex];
  }, [currentTutorial, currentStepIndex]);

  // Calculate progress percentage
  const progressPercentage = useMemo(() => {
    if (!currentTutorial) return 0;
    return Math.round(((currentStepIndex + 1) / currentTutorial.steps.length) * 100);
  }, [currentTutorial, currentStepIndex]);

  // Update spotlight position when step changes
  useEffect(() => {
    if (!currentStep?.target || !isActive) {
      setSpotlightPosition(null);
      return;
    }

    const updateSpotlight = () => {
      const element = document.querySelector(currentStep.target);
      if (element) {
        const rect = element.getBoundingClientRect();
        setSpotlightPosition({
          top: rect.top - 12,
          left: rect.left - 12,
          width: rect.width + 24,
          height: rect.height + 24
        });
      }
    };

    // Delay to allow for DOM updates
    setTimeout(updateSpotlight, 100);
    window.addEventListener('resize', updateSpotlight);
    return () => window.removeEventListener('resize', updateSpotlight);
  }, [currentStep, isActive]);

  // Handle contextual hints
  useEffect(() => {
    if (!showContextualHelp || isActive || !assistantSettings.showHints) {
      setContextualHints([]);
      return;
    }

    const hints = [];
    const elements = document.querySelectorAll('[data-tutorial]');

    elements.forEach(element => {
      const tutorialId = element.getAttribute('data-tutorial');
      const rect = element.getBoundingClientRect();

      if (rect.top >= 0 && rect.bottom <= window.innerHeight && rect.width > 0 && rect.height > 0) {
        hints.push({
          id: tutorialId,
          element,
          position: {
            top: rect.top - 45,
            left: rect.left + rect.width / 2 - 75
          },
          text: getHintText(tutorialId)
        });
      }
    });

    setContextualHints(hints.slice(0, 2)); // Limit to 2 hints to avoid clutter
  }, [showContextualHelp, isActive, assistantSettings.showHints]);

  // Enhanced contextual hint system with workflow awareness
  const getHintText = useCallback((tutorialId) => {
    const workflowStage = detectWorkflowStage();

    const contextualHints = {
      'component-palette': {
        beginner: '🎨 Drag components to start building',
        building: '🎨 Add more components to enhance your app',
        styling: '🎨 Components available for your design',
        testing: '🎨 Add test components if needed',
        exporting: '🎨 All components ready for export'
      },
      'property-editor': {
        beginner: '⚙️ Select a component to customize it',
        building: '⚙️ Fine-tune your component properties',
        styling: '⚙️ Adjust properties to match your theme',
        testing: '⚙️ Configure test scenarios',
        exporting: '⚙️ Final property adjustments'
      },
      'canvas-area': {
        beginner: '🎯 Your workspace - drop components here',
        building: '🎯 Arrange and organize your components',
        styling: '🎯 See your styled components in action',
        testing: '🎯 Test interactions in this area',
        exporting: '🎯 Your completed app layout'
      },
      'preview-mode': {
        beginner: '👁️ See how your app looks to users',
        building: '👁️ Test your app as you build',
        styling: '👁️ Preview your theme changes',
        testing: '👁️ Test user interactions',
        exporting: '👁️ Final preview before export'
      },
      'layout-designer': {
        beginner: '📐 Design responsive layouts',
        building: '📐 Organize your app structure',
        styling: '📐 Layout affects your theme',
        testing: '📐 Test layout responsiveness',
        exporting: '📐 Layout ready for export'
      },
      'theme-manager': {
        beginner: '🎨 Customize colors and fonts',
        building: '🎨 Apply consistent styling',
        styling: '🎨 Perfect your app\'s appearance',
        testing: '🎨 Test theme across components',
        exporting: '🎨 Theme ready for production'
      },
      'testing-tools': {
        beginner: '🧪 Learn about testing your app',
        building: '🧪 Test components as you add them',
        styling: '🧪 Validate your styled components',
        testing: '🧪 Run comprehensive tests',
        exporting: '🧪 Final testing before export'
      },
      'data-management': {
        beginner: '📊 Learn about app data',
        building: '📊 Set up data for your components',
        styling: '📊 Data visualization styling',
        testing: '📊 Test data flows and states',
        exporting: '📊 Data configuration complete'
      },
      'performance-monitor': {
        beginner: '⚡ Monitor your app\'s speed',
        building: '⚡ Check performance as you build',
        styling: '⚡ Optimize theme performance',
        testing: '⚡ Performance testing in progress',
        exporting: '⚡ Final performance check'
      },
      'code-export': {
        beginner: '💻 Export when you\'re ready',
        building: '💻 Export available anytime',
        styling: '💻 Export with your custom theme',
        testing: '💻 Export tested application',
        exporting: '💻 Ready to export your app!'
      },
      'websocket-manager': {
        beginner: '🤝 Collaborate with team members',
        building: '🤝 Real-time collaboration active',
        styling: '🤝 Share styling decisions',
        testing: '🤝 Collaborative testing',
        exporting: '🤝 Team export coordination'
      },
      'tutorial-assistant': {
        beginner: '🎯 Get guided help and tutorials',
        building: '🎯 Context-aware assistance available',
        styling: '🎯 Styling tips and guidance',
        testing: '🎯 Testing guidance and help',
        exporting: '🎯 Export guidance and tips'
      }
    };

    const hints = contextualHints[tutorialId];
    if (hints && hints[workflowStage]) {
      return hints[workflowStage];
    }

    // Fallback to basic hints
    const basicHints = {
      'component-palette': '🎨 Drag components from here',
      'property-editor': '⚙️ Customize properties here',
      'canvas-area': '🎯 Drop components here',
      'preview-mode': '👁️ Preview your app',
      'layout-designer': '📐 Design layouts',
      'theme-manager': '🎨 Customize themes',
      'testing-tools': '🧪 Test your components',
      'data-management': '📊 Manage your data',
      'performance-monitor': '⚡ Monitor performance',
      'code-export': '💻 Export to frameworks',
      'websocket-manager': '🤝 Real-time collaboration',
      'tutorial-assistant': '🎯 Interactive tutorials'
    };

    return basicHints[tutorialId] || '💡 Click for help';
  }, []);

  // Detect current workflow stage based on app state
  const detectWorkflowStage = useCallback(() => {
    // This would integrate with the app state to determine the current stage
    const hasComponents = document.querySelectorAll('[data-tutorial="canvas-area"] .component').length > 0;
    const isPreviewMode = document.querySelector('[data-tutorial="preview-mode"].active');
    const hasThemeApplied = document.querySelector('[data-theme-applied="true"]');
    const isTestingActive = document.querySelector('[data-tutorial="testing-tools"].active');
    const isExporting = document.querySelector('[data-tutorial="code-export"].active');

    if (isExporting) return 'exporting';
    if (isTestingActive) return 'testing';
    if (hasThemeApplied) return 'styling';
    if (hasComponents) return 'building';
    return 'beginner';
  }, []);

  // Handle tutorial start
  const handleStartTutorial = useCallback((tutorialId) => {
    const tutorial = APP_BUILDER_TUTORIALS[tutorialId];
    if (!tutorial) return;

    setCurrentTutorial(tutorial);
    setCurrentStepIndex(0);
    setIsActive(true);
    setIsPaused(false);
    setShowTutorialList(false);

    notification.success({
      message: 'Tutorial Started',
      description: `Starting "${tutorial.title}" tutorial`,
      duration: 3,
      icon: <BookOutlined style={{ color: '#1890ff' }} />
    });
  }, []);

  // Handle next step
  const handleNextStep = useCallback(() => {
    if (!currentTutorial) return;

    if (currentStepIndex < currentTutorial.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Tutorial completed
      handleTutorialComplete();
    }
  }, [currentTutorial, currentStepIndex]);

  // Handle previous step
  const handlePreviousStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  }, [currentStepIndex]);

  // Handle tutorial completion
  const handleTutorialComplete = useCallback(() => {
    if (!currentTutorial) return;

    setCompletedTutorials(prev => new Set([...prev, currentTutorial.id]));
    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);

    if (onTutorialComplete) {
      onTutorialComplete(currentTutorial);
    }

    notification.success({
      message: 'Tutorial Completed! 🎉',
      description: 'Great job! You\'ve mastered this feature.',
      duration: 5,
      icon: <TrophyOutlined style={{ color: '#faad14' }} />
    });
  }, [currentTutorial, onTutorialComplete]);

  // Handle tutorial skip
  const handleTutorialSkip = useCallback(() => {
    if (!currentTutorial) return;

    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);

    if (onTutorialSkip) {
      onTutorialSkip(currentTutorial);
    }

    notification.info({
      message: 'Tutorial Skipped',
      description: 'You can restart it anytime from the tutorial list.',
      duration: 3
    });
  }, [currentTutorial, onTutorialSkip]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Auto-start tutorial for new users with smart detection
  useEffect(() => {
    if (enableAutoStart && !isActive && completedTutorials.size === 0) {
      // Check if user seems to be exploring first
      const hasInteracted = sessionStorage.getItem('app-builder-user-interaction');

      if (!hasInteracted) {
        // Show welcome prompt first
        setTimeout(() => {
          setShowTutorialList(true);
        }, 1500);
      } else {
        // User has interacted, offer contextual help
        setTimeout(() => {
          handleStartTutorial('getting_started');
        }, 3000);
      }
    }
  }, [enableAutoStart, isActive, completedTutorials.size, handleStartTutorial]);

  // Track user interactions for smart tutorial triggering
  useEffect(() => {
    const trackInteraction = () => {
      sessionStorage.setItem('app-builder-user-interaction', 'true');
    };

    document.addEventListener('click', trackInteraction, { once: true });
    document.addEventListener('keydown', trackInteraction, { once: true });

    return () => {
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('keydown', trackInteraction);
    };
  }, []);

  // Enhanced tutorial completion tracking with achievements
  const handleTutorialCompleteEnhanced = useCallback((tutorial) => {
    handleTutorialComplete();

    // Track achievement
    const achievements = JSON.parse(localStorage.getItem('app-builder-achievements') || '[]');
    const newAchievement = {
      id: `tutorial-${tutorial.id}`,
      title: `Completed: ${tutorial.title}`,
      description: tutorial.description,
      earnedAt: new Date().toISOString(),
      type: 'tutorial',
      category: tutorial.category
    };

    achievements.push(newAchievement);
    localStorage.setItem('app-builder-achievements', JSON.stringify(achievements));

    // Show achievement notification
    notification.success({
      message: '🏆 Achievement Unlocked!',
      description: `You completed "${tutorial.title}"`,
      duration: 5,
      style: {
        background: 'linear-gradient(135deg, #faad14 0%, #fa8c16 100%)',
        color: 'white'
      }
    });
  }, [handleTutorialComplete]);

  // Smart tutorial recommendations based on user progress
  const getRecommendedTutorials = useCallback(() => {
    const completed = Array.from(completedTutorials);

    if (completed.length === 0) {
      return ['getting_started'];
    }

    if (completed.includes('getting_started') && !completed.includes('complete_workflow')) {
      return ['complete_workflow'];
    }

    if (completed.includes('complete_workflow') && !completed.includes('advanced_features')) {
      return ['advanced_features'];
    }

    return [];
  }, [completedTutorials]);

  // Context-aware tutorial suggestions
  const getContextualTutorialSuggestions = useCallback(() => {
    const workflowStage = detectWorkflowStage();
    const suggestions = [];

    switch (workflowStage) {
      case 'beginner':
        if (!completedTutorials.has('getting_started')) {
          suggestions.push({
            id: 'getting_started',
            reason: 'Perfect for getting started with App Builder',
            priority: 'high'
          });
        }
        break;
      case 'building':
        if (!completedTutorials.has('complete_workflow')) {
          suggestions.push({
            id: 'complete_workflow',
            reason: 'Learn the complete app creation process',
            priority: 'medium'
          });
        }
        break;
      case 'styling':
        suggestions.push({
          id: 'theme_mastery',
          reason: 'Master advanced theming techniques',
          priority: 'medium'
        });
        break;
      case 'testing':
        if (!completedTutorials.has('advanced_features')) {
          suggestions.push({
            id: 'advanced_features',
            reason: 'Learn comprehensive testing strategies',
            priority: 'high'
          });
        }
        break;
    }

    return suggestions;
  }, [completedTutorials, detectWorkflowStage]);

  return (
    <TutorialContainer>
      {/* Tutorial Overlay */}
      {isActive && !isPaused && (
        <TutorialOverlay allowInteraction={currentStep?.type === 'interactive'}>
          {spotlightPosition && (
            <Spotlight
              style={{
                top: spotlightPosition.top,
                left: spotlightPosition.left,
                width: spotlightPosition.width,
                height: spotlightPosition.height
              }}
            />
          )}
        </TutorialOverlay>
      )}

      {/* Contextual Hints */}
      {contextualHints.map(hint => (
        <ContextualHint
          key={hint.id}
          style={{
            top: hint.position.top,
            left: hint.position.left
          }}
        >
          {hint.text}
        </ContextualHint>
      ))}

      {/* Enhanced Active Tutorial Card */}
      {isActive && currentStep && (
        <TutorialCard
          title={
            <Space>
              <BookOutlined />
              {currentTutorial.title}
              <Badge
                count={`${currentStepIndex + 1}/${currentTutorial.steps.length}`}
                style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
              />
              {currentTutorial.category && (
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  {currentTutorial.category}
                </Tag>
              )}
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleTutorialSkip}
              style={{ color: 'white' }}
              size="small"
            />
          }
        >
          <div>
            <Title level={5} style={{ marginBottom: 8, color: '#1890ff' }}>
              {currentStep.title}
            </Title>
            <Paragraph style={{ marginBottom: 16, color: '#666' }}>
              {currentStep.content}
            </Paragraph>

            <ProgressContainer>
              <StepIndicator>
                <Text type="secondary" style={{ fontSize: 12 }}>Progress</Text>
                <Text strong style={{ color: '#1890ff' }}>{progressPercentage}%</Text>
              </StepIndicator>
              <Progress
                percent={progressPercentage}
                size="small"
                strokeColor="#1890ff"
                trailColor="#f0f0f0"
                showInfo={false}
              />
            </ProgressContainer>

            <Row justify="space-between" align="middle">
              <Col>
                <Space size="small">
                  <Button
                    icon={<StepBackwardOutlined />}
                    onClick={handlePreviousStep}
                    disabled={currentStepIndex === 0}
                    size="small"
                  >
                    Previous
                  </Button>
                  <Button
                    icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                    onClick={handlePauseResume}
                    size="small"
                  >
                    {isPaused ? 'Resume' : 'Pause'}
                  </Button>
                </Space>
              </Col>
              <Col>
                <Space size="small">
                  <Button
                    icon={<StepForwardOutlined />}
                    onClick={handleTutorialSkip}
                    size="small"
                  >
                    Skip
                  </Button>
                  {currentStepIndex === currentTutorial.steps.length - 1 ? (
                    <Button
                      type="primary"
                      icon={<CheckCircleOutlined />}
                      onClick={handleTutorialComplete}
                      size="small"
                    >
                      Complete
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      icon={<StepForwardOutlined />}
                      onClick={handleNextStep}
                      size="small"
                    >
                      Next
                    </Button>
                  )}
                </Space>
              </Col>
            </Row>
          </div>
        </TutorialCard>
      )}

      {/* Floating Tutorial Button */}
      {!isActive && (
        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 24, bottom: 24 }}
          icon={<QuestionCircleOutlined />}
          tooltip="Tutorial Assistant"
        >
          <FloatButton
            icon={<BookOutlined />}
            tooltip="Browse Tutorials"
            onClick={() => setShowTutorialList(true)}
          />
          <FloatButton
            icon={<BulbOutlined />}
            tooltip="Toggle Hints"
            onClick={() => setAssistantSettings(prev => ({ ...prev, showHints: !prev.showHints }))}
          />
          <FloatButton
            icon={<SettingOutlined />}
            tooltip="Tutorial Settings"
            onClick={() => setShowSettings(true)}
          />
        </FloatButton.Group>
      )}

      {/* Enhanced Tutorial List Modal with Recommendations */}
      <Modal
        title={
          <Space>
            <BookOutlined />
            Tutorial Assistant
            <Badge count={Object.keys(APP_BUILDER_TUTORIALS).length - completedTutorials.size} showZero={false} />
          </Space>
        }
        open={showTutorialList}
        onCancel={() => setShowTutorialList(false)}
        footer={[
          <Button key="settings" icon={<SettingOutlined />} onClick={() => setShowSettings(true)}>
            Settings
          </Button>,
          <Button key="close" onClick={() => setShowTutorialList(false)}>
            Close
          </Button>
        ]}
        width={700}
      >
        {/* Contextual Recommendations */}
        {getContextualTutorialSuggestions().length > 0 && (
          <Card
            size="small"
            style={{ marginBottom: 16, background: 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)' }}
            title={
              <Space>
                <BulbOutlined style={{ color: '#1890ff' }} />
                <Text strong>Recommended for You</Text>
              </Space>
            }
          >
            <List
              size="small"
              dataSource={getContextualTutorialSuggestions()}
              renderItem={(suggestion) => {
                const tutorial = APP_BUILDER_TUTORIALS[suggestion.id];
                if (!tutorial) return null;

                return (
                  <List.Item
                    actions={[
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        onClick={() => handleStartTutorial(suggestion.id)}
                      >
                        Start Now
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<Avatar size="small" icon={<RocketOutlined />} style={{ backgroundColor: '#1890ff' }} />}
                      title={tutorial.title}
                      description={
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {suggestion.reason} • {tutorial.estimatedDuration} min
                        </Text>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          </Card>
        )}

        {/* Progress Overview */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ textAlign: 'center' }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={Math.round((completedTutorials.size / Object.keys(APP_BUILDER_TUTORIALS).length) * 100)}
                  format={() => `${completedTutorials.size}/${Object.keys(APP_BUILDER_TUTORIALS).length}`}
                />
                <div style={{ marginTop: 8 }}>
                  <Text strong>Tutorials Completed</Text>
                </div>
              </div>
            </Col>
            <Col span={12}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text type="secondary">Current Stage:</Text>
                  <br />
                  <Tag color="blue">{detectWorkflowStage().charAt(0).toUpperCase() + detectWorkflowStage().slice(1)}</Tag>
                </div>
                <div>
                  <Text type="secondary">Next Recommended:</Text>
                  <br />
                  {getRecommendedTutorials().length > 0 ? (
                    <Tag color="green">{APP_BUILDER_TUTORIALS[getRecommendedTutorials()[0]]?.title}</Tag>
                  ) : (
                    <Tag color="gold">All Caught Up!</Tag>
                  )}
                </div>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* All Tutorials List */}
        <Title level={5}>All Tutorials</Title>
        <List
          dataSource={Object.values(APP_BUILDER_TUTORIALS)}
          renderItem={(tutorial) => {
            const isCompleted = completedTutorials.has(tutorial.id);
            const isRecommended = getRecommendedTutorials().includes(tutorial.id);
            const hasPrerequisites = tutorial.prerequisites && tutorial.prerequisites.length > 0;
            const prerequisitesMet = !hasPrerequisites || tutorial.prerequisites.every(prereq => completedTutorials.has(prereq));

            return (
              <List.Item
                actions={[
                  isCompleted ? (
                    <Space>
                      <Tag color="green" icon={<CheckCircleOutlined />}>
                        Completed
                      </Tag>
                      <Button
                        size="small"
                        icon={<PlayCircleOutlined />}
                        onClick={() => handleStartTutorial(tutorial.id)}
                      >
                        Replay
                      </Button>
                    </Space>
                  ) : prerequisitesMet ? (
                    <Button
                      type={isRecommended ? "primary" : "default"}
                      icon={<PlayCircleOutlined />}
                      onClick={() => handleStartTutorial(tutorial.id)}
                    >
                      {isRecommended ? 'Start (Recommended)' : 'Start'}
                    </Button>
                  ) : (
                    <Tooltip title={`Complete prerequisites: ${tutorial.prerequisites.join(', ')}`}>
                      <Button disabled icon={<PlayCircleOutlined />}>
                        Locked
                      </Button>
                    </Tooltip>
                  )
                ]}
                style={{
                  opacity: prerequisitesMet ? 1 : 0.6,
                  border: isRecommended ? '1px solid #1890ff' : 'none',
                  borderRadius: isRecommended ? 8 : 0,
                  padding: isRecommended ? 12 : 8
                }}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      icon={
                        tutorial.category === 'beginner' ? <BookOutlined /> :
                          tutorial.category === 'intermediate' ? <AppstoreOutlined /> :
                            <RocketOutlined />
                      }
                      style={{
                        backgroundColor:
                          tutorial.category === 'beginner' ? '#52c41a' :
                            tutorial.category === 'intermediate' ? '#faad14' :
                              '#1890ff'
                      }}
                    />
                  }
                  title={
                    <Space>
                      {tutorial.title}
                      <Tag color={
                        tutorial.category === 'beginner' ? 'green' :
                          tutorial.category === 'intermediate' ? 'orange' :
                            'blue'
                      }>
                        {tutorial.category}
                      </Tag>
                      {isRecommended && <Tag color="blue">Recommended</Tag>}
                      {isCompleted && <Tag color="green" icon={<CheckCircleOutlined />}>Done</Tag>}
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph style={{ margin: 0, marginBottom: 4 }}>
                        {tutorial.description}
                      </Paragraph>
                      <Space size="small">
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          ⏱️ {tutorial.estimatedDuration} minutes
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          📋 {tutorial.steps.length} steps
                        </Text>
                        {hasPrerequisites && (
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            🔗 Prerequisites: {tutorial.prerequisites.join(', ')}
                          </Text>
                        )}
                      </Space>
                    </div>
                  }
                />
              </List.Item>
            );
          }}
        />
      </Modal>

      {/* Enhanced Settings Modal */}
      <Modal
        title="Tutorial Settings"
        open={showSettings}
        onCancel={() => setShowSettings(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <TutorialSettingsPanel
          onSettingsChange={(newSettings) => {
            setTutorialSettings(newSettings);
            // Update assistant settings for backward compatibility
            setAssistantSettings(prev => ({
              ...prev,
              autoAdvance: newSettings.autoAdvance,
              showHints: newSettings.showHints,
              playSound: newSettings.playSound,
              animationSpeed: newSettings.animationSpeed
            }));
          }}
          onClose={() => setShowSettings(false)}
          onReset={() => {
            // Reset to default settings
            const defaultSettings = {
              enabled: true,
              showContextualHelp: true,
              showHints: true,
              autoAdvance: false,
              playSound: false,
              animationSpeed: 'normal',
              helpDelay: 2000,
              autoStartForNewUsers: true,
              showProgressNotifications: true,
              enableKeyboardShortcuts: true
            };
            setTutorialSettings(defaultSettings);
            setAssistantSettings(prev => ({
              ...prev,
              autoAdvance: defaultSettings.autoAdvance,
              showHints: defaultSettings.showHints,
              playSound: defaultSettings.playSound,
              animationSpeed: defaultSettings.animationSpeed
            }));
          }}
        />
      </Modal>
    </TutorialContainer>
  );
};

export default IntegratedTutorialAssistant;
