import React, { useEffect } from 'react';
import { Routes as RouterRoutes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { useAuth } from './contexts/AuthContext';
import EnhancedSuspense, { LoadingConfigurations } from './components/loading/EnhancedSuspense';

// Test pages - loaded only when accessed
const QuillTestPage = React.lazy(() =>
  import('./components/QuillTestPage').catch(() => ({ default: () => <div>Test unavailable</div> }))
);
const TemplateSystemDemo = React.lazy(() =>
  import('./pages/TemplateSystemDemo').catch(() => ({ default: () => <div>Demo unavailable</div> }))
);
const SyntaxHighlightingTest = React.lazy(() =>
  import('./pages/SyntaxHighlightingTest').catch(() => ({ default: () => <div>Test unavailable</div> }))
);
const ExportPreviewTest = React.lazy(() =>
  import('./pages/ExportPreviewTest').catch(() => ({ default: () => <div>Test unavailable</div> }))
);
const WebSocketRealTimeTest = React.lazy(() =>
  import('./pages/WebSocketRealTimeTest').catch(() => ({ default: () => <div>Test unavailable</div> }))
);

// Direct import for HomePageMVP to avoid lazy loading issues
const HomePageMVPDirect = React.lazy(() => import('./pages/HomePageMVP'));

// Import the new integrated App Builder page
const AppBuilderIntegratedPage = React.lazy(() => import('./pages/AppBuilderIntegrated'));

// Simple app builder page - loads only when needed
const IntegratedAppBuilderPage = React.lazy(() =>
  import('./components/builder/SimpleAppBuilder').catch(() => ({
    default: () => (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>App Builder Unavailable</h2>
        <p>The app builder is temporarily unavailable. Please try again later.</p>
      </div>
    )
  }))
);

// Enhanced route configuration with priority-based loading
import {
  HighPriorityRoutes,
  MediumPriorityRoutes,
  LowPriorityRoutes,
  AuthRoutes,
  UtilityRoutes,
  TestRoutes,
  preloadRoutesByPriority
} from './config/routeConfig';

// Loading component for suspense fallback
const PageLoading = () => (
  <LoadingSpinner
    tip="Loading page..."
    fullScreen={true}
    backgroundColor="rgba(255, 255, 255, 0.9)"
  />
);

/**
 * Application routes with enhanced lazy loading and priority-based code splitting
 * Updated to include authentication and project management
 */
const Routes = () => {
  const { isAuthenticated } = useAuth();

  // Initialize progressive route loading
  useEffect(() => {
    preloadRoutesByPriority();
  }, []);

  return (
    <Layout>
      <EnhancedSuspense
        componentName="Routes"
        loadingMessage="Loading page..."
        loadingDescription="Please wait while the page loads"
        {...LoadingConfigurations.page}
        timeout={15000}
      >
        <RouterRoutes>
          {/* MVP-focused homepage */}
          <Route path="/" element={<HomePageMVPDirect />} />

          <Route path="/home" element={<HighPriorityRoutes.HomePage />} />
          <Route path="/home-mvp" element={<HighPriorityRoutes.HomePageMVP />} />
          <Route path="/mvp" element={<HighPriorityRoutes.AppBuilderMVP />} />

          {/* Public templates route */}
          <Route path="/templates" element={<LowPriorityRoutes.TemplatesPage />} />

          {/* Template System Demo route */}
          <Route path="/template-system-demo" element={<TemplateSystemDemo />} />

          {/* Syntax Highlighting Test route */}
          <Route path="/syntax-test" element={<SyntaxHighlightingTest />} />

          {/* Export Preview Test route */}
          <Route path="/export-test" element={<ExportPreviewTest />} />

          {/* WebSocket Real-time Test route */}
          <Route path="/websocket-test" element={<WebSocketRealTimeTest />} />

          {/* Authentication routes */}
          <Route path="/login" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <AuthRoutes.LoginPage />
          } />
          <Route path="/register" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <AuthRoutes.RegisterPage />
          } />
          <Route path="/forgot-password" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <AuthRoutes.ForgotPasswordPage />
          } />
          <Route path="/reset-password/:token" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <AuthRoutes.ResetPasswordPage />
          } />

          {/* Protected routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <MediumPriorityRoutes.DashboardPage />
            </ProtectedRoute>
          } />
          <Route path="/app-builder" element={
            <ProtectedRoute>
              <AppBuilderIntegratedPage />
            </ProtectedRoute>
          } />
          <Route path="/app-builder-advanced" element={
            <ProtectedRoute>
              <IntegratedAppBuilderPage />
            </ProtectedRoute>
          } />
          <Route path="/app-builder-enhanced" element={
            <ProtectedRoute>
              <MediumPriorityRoutes.AppBuilderWithTheme />
            </ProtectedRoute>
          } />
          <Route path="/websocket" element={
            <ProtectedRoute>
              <LowPriorityRoutes.WebSocketPage />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <LowPriorityRoutes.ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="/projects" element={
            <ProtectedRoute>
              <MediumPriorityRoutes.ProjectsPage />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <LowPriorityRoutes.SettingsPage />
            </ProtectedRoute>
          } />

          {/* Test routes */}
          <Route path="/theme-test" element={<TestRoutes.ThemeTest />} />
          <Route path="/dark-mode-test" element={<TestRoutes.DarkModeTest />} />
          <Route path="/contrast-test" element={<TestRoutes.ContrastTest />} />
          <Route path="/header-contrast-test" element={<TestRoutes.HeaderContrastTest />} />
          <Route path="/service-worker-test" element={<TestRoutes.ServiceWorkerTest />} />
          <Route path="/responsive-demo" element={<TestRoutes.ResponsiveDemo />} />
          <Route path="/quill-test" element={<QuillTestPage />} />
          <Route path="/collaboration-test" element={<TestRoutes.CollaborationTest />} />

          {/* Error routes */}
          <Route path="/unauthorized" element={<UtilityRoutes.UnauthorizedPage />} />
          <Route path="/404" element={<UtilityRoutes.NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" />} />
        </RouterRoutes>
      </EnhancedSuspense>
    </Layout>
  );
};

export default Routes;
