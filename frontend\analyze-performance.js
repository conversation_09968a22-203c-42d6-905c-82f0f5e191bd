#!/usr/bin/env node

/**
 * Performance Analysis Script
 * Analyzes bundle size, dependencies, and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');

console.log('=== App Builder Performance Analysis ===\n');

// 1. Analyze build output
console.log('1. Build Output Analysis');
console.log('========================');

const buildDir = './build/static/js';
if (fs.existsSync(buildDir)) {
  const files = fs.readdirSync(buildDir);
  let totalSize = 0;
  
  console.log('JavaScript Bundle Files:');
  files.forEach(file => {
    if (file.endsWith('.js')) {
      const filePath = path.join(buildDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      totalSize += stats.size;
      
      console.log(`  ${file}: ${sizeKB} KB (${sizeMB} MB)`);
    }
  });
  
  const totalKB = (totalSize / 1024).toFixed(2);
  const totalMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`\nTotal JS Bundle Size: ${totalKB} KB (${totalMB} MB)`);
  
  // Performance assessment
  if (totalSize > 5 * 1024 * 1024) { // > 5MB
    console.log('⚠️  WARNING: Bundle size is very large (>5MB)');
  } else if (totalSize > 2 * 1024 * 1024) { // > 2MB
    console.log('⚠️  CAUTION: Bundle size is large (>2MB)');
  } else {
    console.log('✅ Bundle size is acceptable');
  }
} else {
  console.log('❌ Build directory not found. Run "npm run build" first.');
}

// 2. Analyze package.json dependencies
console.log('\n2. Dependency Analysis');
console.log('======================');

try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  const deps = packageJson.dependencies || {};
  const devDeps = packageJson.devDependencies || {};
  
  console.log(`Production Dependencies: ${Object.keys(deps).length}`);
  console.log(`Development Dependencies: ${Object.keys(devDeps).length}`);
  
  // Large dependencies that might need optimization
  const largeDependencies = [
    'antd',
    'react-syntax-highlighter',
    '@ant-design/charts',
    '@ant-design/pro-components',
    'recharts',
    'styled-components'
  ];
  
  console.log('\nLarge Dependencies Found:');
  largeDependencies.forEach(dep => {
    if (deps[dep]) {
      console.log(`  ✓ ${dep}: ${deps[dep]}`);
    }
  });
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// 3. Analyze webpack stats (if available)
console.log('\n3. Webpack Bundle Analysis');
console.log('==========================');

// Check for webpack-bundle-analyzer output
const statsFile = './build/static/js/stats.json';
if (fs.existsSync(statsFile)) {
  console.log('✅ Webpack stats file found');
  // Could parse and analyze webpack stats here
} else {
  console.log('ℹ️  No webpack stats file found. Run "npm run build:analyze" to generate detailed analysis.');
}

// 4. Performance Recommendations
console.log('\n4. Performance Optimization Recommendations');
console.log('===========================================');

const recommendations = [
  {
    category: 'Bundle Splitting',
    items: [
      'Implement route-based code splitting for better initial load times',
      'Split vendor libraries into separate chunks',
      'Use dynamic imports for large components'
    ]
  },
  {
    category: 'Ant Design Optimization',
    items: [
      'Use babel-plugin-import for tree shaking',
      'Import only required Ant Design components',
      'Consider using a custom theme to reduce CSS size'
    ]
  },
  {
    category: 'React Syntax Highlighter',
    items: [
      'Load syntax highlighter languages dynamically',
      'Use lighter themes or create custom themes',
      'Consider lazy loading for code preview components'
    ]
  },
  {
    category: 'General Optimizations',
    items: [
      'Enable gzip compression on the server',
      'Implement service worker for caching',
      'Optimize images and use WebP format',
      'Remove unused dependencies and code'
    ]
  }
];

recommendations.forEach(rec => {
  console.log(`\n${rec.category}:`);
  rec.items.forEach(item => {
    console.log(`  • ${item}`);
  });
});

// 5. Quick Wins
console.log('\n5. Quick Performance Wins');
console.log('=========================');

const quickWins = [
  'Add React.lazy() to more components',
  'Implement proper error boundaries',
  'Use React.memo for expensive components',
  'Optimize re-renders with useCallback and useMemo',
  'Enable webpack production optimizations',
  'Use CDN for static assets'
];

quickWins.forEach((win, index) => {
  console.log(`${index + 1}. ${win}`);
});

// 6. Performance Targets
console.log('\n6. Performance Targets');
console.log('======================');

console.log('Recommended Bundle Sizes:');
console.log('  • Initial JS Bundle: < 244 KB (gzipped)');
console.log('  • Total JS (all chunks): < 1 MB (gzipped)');
console.log('  • CSS Bundle: < 50 KB (gzipped)');
console.log('  • First Contentful Paint: < 1.5s');
console.log('  • Time to Interactive: < 3.5s');

// 7. Current Status Summary
console.log('\n7. Current Status Summary');
console.log('=========================');

const currentStatus = [
  '✅ React 18 with modern build tools',
  '✅ Webpack 5 with optimization enabled',
  '✅ Code splitting partially implemented',
  '⚠️  Large bundle size needs optimization',
  '⚠️  Ant Design imports could be optimized',
  '✅ WebSocket functionality working',
  '✅ Syntax highlighting working'
];

currentStatus.forEach(status => {
  console.log(status);
});

console.log('\n=== Analysis Complete ===');
console.log('\nNext Steps:');
console.log('1. Run "npm run build:analyze" for detailed bundle analysis');
console.log('2. Implement code splitting for routes');
console.log('3. Optimize Ant Design imports');
console.log('4. Add performance monitoring');
console.log('5. Test with Lighthouse for web vitals');
