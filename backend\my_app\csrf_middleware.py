# backend/my_app/csrf_middleware.py
from django.middleware.csrf import CsrfViewMiddleware
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class CustomCsrfMiddleware(CsrfViewMiddleware):
    """
    Custom CSRF middleware that exempts certain API endpoints from CSRF protection
    while ensuring proper CSRF validation for all other endpoints.
    """

    def process_view(self, request, callback, callback_args, callback_kwargs):
        # List of URL patterns to exempt from CSRF protection
        exempt_patterns = [
            '/api/errors/',
            '/graphql/',
            '/api/csrf-token/',  # Allow CSRF token endpoint without CSRF protection
            '/api/health/',      # Allow health checks without CSRF protection
            '/api/status/',      # Allow status checks without CSRF protection
            '/api/health-check/', # Alternative health check endpoint
            '/ws/',              # WebSocket connections
            '/admin/jsi18n/',    # Django admin i18n
            '/api/auth/login/',  # Unified auth login endpoint
            '/api/auth/register/', # Unified auth register endpoint
            '/api/auth/refresh/', # Unified auth refresh endpoint
            '/api/auth/logout/',  # Unified auth logout endpoint
            '/api/auth/verify/',  # Unified auth verify endpoint
        ]

        # Check if the request path matches any exempt pattern
        for pattern in exempt_patterns:
            if request.path.startswith(pattern):
                if settings.DEBUG:
                    logger.debug(f"CSRF exempted for path: {request.path}")
                return None  # Skip CSRF protection

        # For all other endpoints, apply CSRF protection
        if settings.DEBUG:
            logger.debug(f"CSRF protection applied for path: {request.path}")

        return super().process_view(request, callback, callback_args, callback_kwargs)

    def process_response(self, request, response):
        """
        Ensure CSRF cookie is set for all responses that might need it.
        """
        response = super().process_response(request, response)

        # Set CSRF cookie for API endpoints that will need it
        if (request.path.startswith('/api/') and
            not any(request.path.startswith(pattern) for pattern in [
                '/api/csrf-token/', '/api/health/', '/api/status/', '/api/errors/'
            ])):
            # Ensure CSRF cookie is available for subsequent requests
            from django.middleware.csrf import get_token
            get_token(request)

        return response



