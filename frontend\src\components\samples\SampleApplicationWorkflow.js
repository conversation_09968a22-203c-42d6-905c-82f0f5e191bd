/**
 * Sample Application Workflow Component
 * 
 * This component demonstrates the complete App Builder workflow:
 * 1. Component selection from palette
 * 2. Drag and drop to canvas
 * 3. Layout design and positioning
 * 4. Property editing and customization
 * 5. Theme application
 * 6. Code export
 * 
 * This serves as both a practical example and a tutorial demonstration.
 */

import React, { useState, useCallback, useEffect } from 'react';
import { 
  Card, 
  Steps, 
  Button, 
  Typography, 
  Space, 
  Alert, 
  Divider,
  Tag,
  Progress,
  message
} from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  RocketOutlined,
  BulbOutlined,
  ToolOutlined,
  ExportOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Sample application templates
const SAMPLE_APPLICATIONS = [
  {
    id: 'landing-page',
    name: 'Landing Page',
    description: 'A modern landing page with header, hero section, features, and footer',
    difficulty: 'Beginner',
    estimatedTime: '10 minutes',
    components: [
      { type: 'header', label: 'Navigation Header' },
      { type: 'section', label: 'Hero Section' },
      { type: 'text', label: 'Headline Text' },
      { type: 'button', label: 'Call-to-Action Button' },
      { type: 'card', label: 'Feature Cards' },
      { type: 'section', label: 'Footer Section' }
    ],
    steps: [
      'Add header component for navigation',
      'Create hero section with headline',
      'Add call-to-action button',
      'Design feature cards layout',
      'Add footer section',
      'Apply theme and styling',
      'Export final code'
    ]
  },
  {
    id: 'dashboard',
    name: 'Admin Dashboard',
    description: 'A comprehensive dashboard with charts, tables, and widgets',
    difficulty: 'Intermediate',
    estimatedTime: '20 minutes',
    components: [
      { type: 'header', label: 'Dashboard Header' },
      { type: 'card', label: 'Stats Cards' },
      { type: 'section', label: 'Chart Section' },
      { type: 'card', label: 'Data Table' },
      { type: 'button', label: 'Action Buttons' }
    ],
    steps: [
      'Create dashboard header with navigation',
      'Add statistics cards',
      'Insert chart components',
      'Add data table',
      'Configure action buttons',
      'Apply dashboard theme',
      'Export with TypeScript'
    ]
  },
  {
    id: 'contact-form',
    name: 'Contact Form',
    description: 'A responsive contact form with validation and submission',
    difficulty: 'Beginner',
    estimatedTime: '8 minutes',
    components: [
      { type: 'header', label: 'Form Header' },
      { type: 'input', label: 'Name Input' },
      { type: 'input', label: 'Email Input' },
      { type: 'input', label: 'Message Input' },
      { type: 'button', label: 'Submit Button' }
    ],
    steps: [
      'Add form header',
      'Create input fields',
      'Add validation rules',
      'Style form layout',
      'Add submit button',
      'Test form functionality',
      'Export with validation'
    ]
  }
];

// Workflow steps configuration
const WORKFLOW_STEPS = [
  {
    title: 'Select Template',
    description: 'Choose a sample application to build',
    icon: <BulbOutlined />,
    status: 'wait'
  },
  {
    title: 'Drag Components',
    description: 'Drag components from palette to canvas',
    icon: <ToolOutlined />,
    status: 'wait'
  },
  {
    title: 'Design Layout',
    description: 'Position and arrange components',
    icon: <PlayCircleOutlined />,
    status: 'wait'
  },
  {
    title: 'Customize Properties',
    description: 'Edit component properties and styling',
    icon: <ToolOutlined />,
    status: 'wait'
  },
  {
    title: 'Apply Theme',
    description: 'Choose and apply a theme',
    icon: <BulbOutlined />,
    status: 'wait'
  },
  {
    title: 'Export Code',
    description: 'Generate and download the final code',
    icon: <ExportOutlined />,
    status: 'wait'
  }
];

export default function SampleApplicationWorkflow({ 
  onStartWorkflow, 
  onStepComplete,
  currentStep = 0,
  isActive = false 
}) {
  const [selectedSample, setSelectedSample] = useState(null);
  const [workflowSteps, setWorkflowSteps] = useState(WORKFLOW_STEPS);
  const [progress, setProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);

  // Update step status
  const updateStepStatus = useCallback((stepIndex, status) => {
    setWorkflowSteps(prev => prev.map((step, index) => 
      index === stepIndex ? { ...step, status } : step
    ));
  }, []);

  // Calculate progress
  useEffect(() => {
    const completedSteps = workflowSteps.filter(step => step.status === 'finish').length;
    const newProgress = (completedSteps / workflowSteps.length) * 100;
    setProgress(newProgress);
  }, [workflowSteps]);

  // Handle sample selection
  const handleSampleSelect = useCallback((sample) => {
    setSelectedSample(sample);
    message.success(`Selected ${sample.name} template`);
  }, []);

  // Start workflow
  const handleStartWorkflow = useCallback(() => {
    if (!selectedSample) {
      message.warning('Please select a sample application first');
      return;
    }

    setIsRunning(true);
    updateStepStatus(0, 'finish');
    updateStepStatus(1, 'process');
    
    if (onStartWorkflow) {
      onStartWorkflow(selectedSample);
    }

    message.success(`Starting ${selectedSample.name} workflow`);
  }, [selectedSample, onStartWorkflow, updateStepStatus]);

  // Complete current step
  const handleStepComplete = useCallback((stepIndex) => {
    updateStepStatus(stepIndex, 'finish');
    
    if (stepIndex < workflowSteps.length - 1) {
      updateStepStatus(stepIndex + 1, 'process');
    } else {
      setIsRunning(false);
      message.success('Workflow completed successfully!');
    }

    if (onStepComplete) {
      onStepComplete(stepIndex);
    }
  }, [workflowSteps.length, onStepComplete, updateStepStatus]);

  // Reset workflow
  const handleReset = useCallback(() => {
    setWorkflowSteps(WORKFLOW_STEPS.map(step => ({ ...step, status: 'wait' })));
    setSelectedSample(null);
    setIsRunning(false);
    setProgress(0);
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <RocketOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        Sample Application Workflow
      </Title>
      
      <Paragraph>
        Follow this guided workflow to build a complete application using the App Builder's 
        drag-and-drop interface. This demonstrates the full development process from component 
        selection to code export.
      </Paragraph>

      {/* Progress indicator */}
      {isRunning && (
        <Card style={{ marginBottom: '24px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>Workflow Progress</Text>
            <Progress 
              percent={progress} 
              status={progress === 100 ? 'success' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </Space>
        </Card>
      )}

      {/* Sample application selection */}
      <Card title="Step 1: Choose a Sample Application" style={{ marginBottom: '24px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
          {SAMPLE_APPLICATIONS.map((sample) => (
            <Card
              key={sample.id}
              size="small"
              hoverable
              style={{
                border: selectedSample?.id === sample.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                cursor: 'pointer'
              }}
              onClick={() => handleSampleSelect(sample)}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Title level={5} style={{ margin: 0 }}>{sample.name}</Title>
                  <Tag color={sample.difficulty === 'Beginner' ? 'green' : 'orange'}>
                    {sample.difficulty}
                  </Tag>
                </div>
                
                <Text type="secondary">{sample.description}</Text>
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text strong>⏱️ {sample.estimatedTime}</Text>
                  <Text strong>🧩 {sample.components.length} components</Text>
                </div>

                <Divider style={{ margin: '8px 0' }} />
                
                <div>
                  <Text strong style={{ fontSize: '12px' }}>Components:</Text>
                  <div style={{ marginTop: '4px' }}>
                    {sample.components.slice(0, 3).map((comp, index) => (
                      <Tag key={index} size="small" style={{ margin: '2px' }}>
                        {comp.label}
                      </Tag>
                    ))}
                    {sample.components.length > 3 && (
                      <Tag size="small" style={{ margin: '2px' }}>
                        +{sample.components.length - 3} more
                      </Tag>
                    )}
                  </div>
                </div>
              </Space>
            </Card>
          ))}
        </div>
      </Card>

      {/* Workflow steps */}
      <Card title="Step 2: Follow the Workflow" style={{ marginBottom: '24px' }}>
        <Steps direction="vertical" current={currentStep}>
          {workflowSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              status={step.status}
              icon={step.icon}
            />
          ))}
        </Steps>
      </Card>

      {/* Selected sample details */}
      {selectedSample && (
        <Card title={`Building: ${selectedSample.name}`} style={{ marginBottom: '24px' }}>
          <Alert
            message="Ready to Start"
            description={`You've selected the ${selectedSample.name} template. Click "Start Workflow" to begin the guided building process.`}
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          
          <Title level={5}>Step-by-Step Instructions:</Title>
          <ol>
            {selectedSample.steps.map((step, index) => (
              <li key={index} style={{ marginBottom: '4px' }}>
                <Text>{step}</Text>
              </li>
            ))}
          </ol>
        </Card>
      )}

      {/* Action buttons */}
      <Card>
        <Space>
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleStartWorkflow}
            disabled={!selectedSample || isRunning}
          >
            Start Workflow
          </Button>
          
          <Button
            size="large"
            onClick={handleReset}
            disabled={!selectedSample && !isRunning}
          >
            Reset
          </Button>
          
          {isRunning && (
            <Button
              type="dashed"
              size="large"
              icon={<CheckCircleOutlined />}
              onClick={() => handleStepComplete(currentStep)}
            >
              Complete Current Step
            </Button>
          )}
        </Space>
      </Card>
    </div>
  );
}
