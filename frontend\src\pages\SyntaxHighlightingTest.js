import React from 'react';
import { Card, Typography, Space, Divider } from 'antd';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Title, Text } = Typography;

/**
 * Syntax Highlighting Test Page
 * Tests react-syntax-highlighter functionality with different languages and themes
 */
const SyntaxHighlightingTest = () => {
  const codeExamples = {
    javascript: `// JavaScript Example
import React, { useState, useEffect } from 'react';
import { But<PERSON>, Card } from 'antd';

const MyComponent = () => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    console.log('Component mounted');
  }, []);

  const handleClick = () => {
    setCount(prev => prev + 1);
  };

  return (
    <Card title="Counter">
      <p>Count: {count}</p>
      <Button onClick={handleClick}>
        Increment
      </Button>
    </Card>
  );
};

export default MyComponent;`,

    python: `# Python Example
import json
import asyncio
from typing import Dict, List, Optional

class DataProcessor:
    def __init__(self, config: Dict):
        self.config = config
        self.data = []
    
    async def process_data(self, items: List[Dict]) -> Optional[Dict]:
        """Process a list of data items"""
        try:
            results = []
            for item in items:
                processed = await self._process_item(item)
                if processed:
                    results.append(processed)
            
            return {
                'success': True,
                'count': len(results),
                'data': results
            }
        except Exception as e:
            print(f"Error processing data: {e}")
            return None
    
    async def _process_item(self, item: Dict) -> Optional[Dict]:
        # Simulate async processing
        await asyncio.sleep(0.1)
        return {**item, 'processed': True}`,

    css: `/* CSS Example */
.app-builder {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.component-palette {
  width: 300px;
  background: #ffffff;
  border-right: 1px solid #e8e8e8;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.component-palette:hover {
  box-shadow: 4px 0 16px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .component-palette {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    z-index: 1000;
  }
  
  .component-palette.open {
    left: 0;
  }
}`,

    json: `{
  "name": "app-builder-201",
  "version": "1.0.0",
  "description": "Advanced App Builder with React",
  "main": "src/index.js",
  "scripts": {
    "start": "webpack serve --mode development",
    "build": "webpack --mode production",
    "test": "jest",
    "lint": "eslint src/"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "antd": "^5.24.6",
    "react-syntax-highlighter": "15.6.1"
  },
  "devDependencies": {
    "webpack": "^5.99.7",
    "babel-loader": "^9.1.3",
    "@babel/core": "^7.23.9"
  },
  "keywords": ["react", "app-builder", "components"],
  "author": "App Builder Team",
  "license": "MIT"
}`
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={1}>Syntax Highlighting Test</Title>
      <Text type="secondary">
        Testing react-syntax-highlighter functionality with different languages and themes
      </Text>

      <Divider />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* JavaScript with Tomorrow theme */}
        <Card title="JavaScript (Tomorrow Theme)" size="small">
          <SyntaxHighlighter
            language="javascript"
            style={tomorrow}
            customStyle={{
              margin: 0,
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            {codeExamples.javascript}
          </SyntaxHighlighter>
        </Card>

        {/* Python with VS Code Dark Plus theme */}
        <Card title="Python (VS Code Dark Plus Theme)" size="small">
          <SyntaxHighlighter
            language="python"
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            {codeExamples.python}
          </SyntaxHighlighter>
        </Card>

        {/* CSS with Tomorrow theme */}
        <Card title="CSS (Tomorrow Theme)" size="small">
          <SyntaxHighlighter
            language="css"
            style={tomorrow}
            customStyle={{
              margin: 0,
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            {codeExamples.css}
          </SyntaxHighlighter>
        </Card>

        {/* JSON with VS Code Dark Plus theme */}
        <Card title="JSON (VS Code Dark Plus Theme)" size="small">
          <SyntaxHighlighter
            language="json"
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            {codeExamples.json}
          </SyntaxHighlighter>
        </Card>

        {/* Test Status */}
        <Card title="Test Status" type="inner">
          <Space direction="vertical">
            <Text>✅ react-syntax-highlighter package: Installed (v15.6.1)</Text>
            <Text>✅ Prism component: Working</Text>
            <Text>✅ Tomorrow theme: Loaded</Text>
            <Text>✅ VS Code Dark Plus theme: Loaded</Text>
            <Text>✅ Multiple languages: Supported (JavaScript, Python, CSS, JSON)</Text>
            <Text>✅ Custom styling: Applied</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default SyntaxHighlightingTest;
