/**
 * Tutorial Settings Panel Component
 * 
 * Provides user-friendly controls to enable/disable tutorial assistance,
 * customize tutorial behavior, and manage tutorial preferences.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Switch, 
  Slider, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Divider,
  Row,
  Col,
  Alert,
  Tooltip,
  Badge,
  Tag
} from 'antd';
import {
  SettingOutlined,
  BulbOutlined,
  SoundOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// Styled Components
const SettingsContainer = styled.div`
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
`;

const SettingGroup = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const PresetCard = styled(Card)`
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid ${props => props.selected ? '#1890ff' : '#f0f0f0'};
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }
  
  .ant-card-body {
    padding: 12px;
  }
`;

// Default settings
const DEFAULT_SETTINGS = {
  enabled: true,
  showContextualHelp: true,
  showHints: true,
  autoAdvance: false,
  playSound: false,
  animationSpeed: 'normal',
  helpDelay: 2000,
  autoStartForNewUsers: true,
  showProgressNotifications: true,
  enableKeyboardShortcuts: true,
  theme: 'auto'
};

// Preset configurations
const PRESET_CONFIGURATIONS = {
  beginner: {
    name: 'Beginner Friendly',
    description: 'Maximum guidance and assistance',
    icon: '🌱',
    settings: {
      enabled: true,
      showContextualHelp: true,
      showHints: true,
      autoAdvance: false,
      playSound: true,
      animationSpeed: 'slow',
      helpDelay: 1500,
      autoStartForNewUsers: true,
      showProgressNotifications: true,
      enableKeyboardShortcuts: true
    }
  },
  intermediate: {
    name: 'Balanced',
    description: 'Moderate assistance with some automation',
    icon: '⚖️',
    settings: {
      enabled: true,
      showContextualHelp: true,
      showHints: true,
      autoAdvance: false,
      playSound: false,
      animationSpeed: 'normal',
      helpDelay: 2000,
      autoStartForNewUsers: false,
      showProgressNotifications: true,
      enableKeyboardShortcuts: true
    }
  },
  expert: {
    name: 'Minimal',
    description: 'Minimal assistance for experienced users',
    icon: '🚀',
    settings: {
      enabled: true,
      showContextualHelp: false,
      showHints: false,
      autoAdvance: true,
      playSound: false,
      animationSpeed: 'fast',
      helpDelay: 3000,
      autoStartForNewUsers: false,
      showProgressNotifications: false,
      enableKeyboardShortcuts: true
    }
  },
  disabled: {
    name: 'Disabled',
    description: 'Turn off all tutorial assistance',
    icon: '🔇',
    settings: {
      enabled: false,
      showContextualHelp: false,
      showHints: false,
      autoAdvance: false,
      playSound: false,
      animationSpeed: 'normal',
      helpDelay: 2000,
      autoStartForNewUsers: false,
      showProgressNotifications: false,
      enableKeyboardShortcuts: false
    }
  }
};

/**
 * Tutorial Settings Panel Component
 */
const TutorialSettingsPanel = ({
  onSettingsChange,
  onClose,
  onReset
}) => {
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);
  const [selectedPreset, setSelectedPreset] = useState('intermediate');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load settings on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('app-builder-tutorial-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      }
    } catch (error) {
      console.warn('Failed to load tutorial settings:', error);
    }
  }, []);

  // Handle setting change
  const handleSettingChange = useCallback((key, value) => {
    setSettings(prev => {
      const newSettings = { ...prev, [key]: value };
      setHasUnsavedChanges(true);
      return newSettings;
    });
  }, []);

  // Apply preset
  const handlePresetSelect = useCallback((presetKey) => {
    const preset = PRESET_CONFIGURATIONS[presetKey];
    if (preset) {
      setSettings(preset.settings);
      setSelectedPreset(presetKey);
      setHasUnsavedChanges(true);
    }
  }, []);

  // Save settings
  const handleSave = useCallback(() => {
    try {
      localStorage.setItem('app-builder-tutorial-settings', JSON.stringify(settings));
      setHasUnsavedChanges(false);
      
      if (onSettingsChange) {
        onSettingsChange(settings);
      }
      
      // Show success message
      const event = new CustomEvent('showContextualHelp', {
        detail: {
          text: 'Tutorial settings saved successfully!',
          title: 'Settings Saved',
          element: null,
          priority: 'medium'
        }
      });
      window.dispatchEvent(event);
    } catch (error) {
      console.error('Failed to save tutorial settings:', error);
    }
  }, [settings, onSettingsChange]);

  // Reset to defaults
  const handleReset = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    setSelectedPreset('intermediate');
    setHasUnsavedChanges(true);
    
    if (onReset) {
      onReset();
    }
  }, [onReset]);

  // Render preset cards
  const renderPresetCards = () => {
    return (
      <Row gutter={[12, 12]}>
        {Object.entries(PRESET_CONFIGURATIONS).map(([key, preset]) => (
          <Col span={12} key={key}>
            <PresetCard
              size="small"
              selected={selectedPreset === key}
              onClick={() => handlePresetSelect(key)}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, marginBottom: 8 }}>{preset.icon}</div>
                <Text strong style={{ fontSize: 12 }}>{preset.name}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 10 }}>
                  {preset.description}
                </Text>
              </div>
            </PresetCard>
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <SettingsContainer>
      {/* Header */}
      <div style={{ marginBottom: 20 }}>
        <Title level={4} style={{ margin: 0, marginBottom: 8 }}>
          <Space>
            <SettingOutlined />
            Tutorial Settings
            {hasUnsavedChanges && <Badge dot />}
          </Space>
        </Title>
        <Paragraph type="secondary" style={{ margin: 0 }}>
          Customize your tutorial experience and assistance preferences
        </Paragraph>
      </div>

      {/* Quick Presets */}
      <SettingGroup>
        <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
          <Space>
            <QuestionCircleOutlined />
            Quick Presets
          </Space>
        </Title>
        {renderPresetCards()}
      </SettingGroup>

      {/* Main Settings */}
      <SettingGroup>
        <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
          <Space>
            <BulbOutlined />
            Tutorial Assistance
          </Space>
        </Title>
        
        <SettingItem>
          <div>
            <Text strong>Enable Tutorial System</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Turn on/off all tutorial features
            </Text>
          </div>
          <Switch
            checked={settings.enabled}
            onChange={(checked) => handleSettingChange('enabled', checked)}
          />
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Show Contextual Help</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Display helpful hints when hovering over elements
            </Text>
          </div>
          <Switch
            checked={settings.showContextualHelp}
            onChange={(checked) => handleSettingChange('showContextualHelp', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Auto-start for New Users</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Automatically start tutorials for first-time users
            </Text>
          </div>
          <Switch
            checked={settings.autoStartForNewUsers}
            onChange={(checked) => handleSettingChange('autoStartForNewUsers', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>
      </SettingGroup>

      {/* Visual & Audio Settings */}
      <SettingGroup>
        <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
          <Space>
            <EyeOutlined />
            Visual & Audio
          </Space>
        </Title>

        <SettingItem>
          <div>
            <Text strong>Animation Speed</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Control the speed of tutorial animations
            </Text>
          </div>
          <Select
            value={settings.animationSpeed}
            onChange={(value) => handleSettingChange('animationSpeed', value)}
            style={{ width: 100 }}
            disabled={!settings.enabled}
          >
            <Option value="slow">Slow</Option>
            <Option value="normal">Normal</Option>
            <Option value="fast">Fast</Option>
          </Select>
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Help Delay (seconds)</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              How long to wait before showing contextual help
            </Text>
          </div>
          <div style={{ width: 120 }}>
            <Slider
              min={0.5}
              max={5}
              step={0.5}
              value={settings.helpDelay / 1000}
              onChange={(value) => handleSettingChange('helpDelay', value * 1000)}
              disabled={!settings.enabled}
            />
            <Text type="secondary" style={{ fontSize: 10 }}>
              {settings.helpDelay / 1000}s
            </Text>
          </div>
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Sound Effects</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Play sounds for tutorial interactions
            </Text>
          </div>
          <Switch
            checked={settings.playSound}
            onChange={(checked) => handleSettingChange('playSound', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>
      </SettingGroup>

      {/* Advanced Settings */}
      <SettingGroup>
        <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
          <Space>
            <ClockCircleOutlined />
            Advanced
          </Space>
        </Title>

        <SettingItem>
          <div>
            <Text strong>Auto-advance Steps</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Automatically move to next step after completion
            </Text>
          </div>
          <Switch
            checked={settings.autoAdvance}
            onChange={(checked) => handleSettingChange('autoAdvance', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Progress Notifications</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Show notifications for achievements and progress
            </Text>
          </div>
          <Switch
            checked={settings.showProgressNotifications}
            onChange={(checked) => handleSettingChange('showProgressNotifications', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>

        <SettingItem>
          <div>
            <Text strong>Keyboard Shortcuts</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Enable keyboard shortcuts for tutorial navigation
            </Text>
          </div>
          <Switch
            checked={settings.enableKeyboardShortcuts}
            onChange={(checked) => handleSettingChange('enableKeyboardShortcuts', checked)}
            disabled={!settings.enabled}
          />
        </SettingItem>
      </SettingGroup>

      {/* Unsaved Changes Alert */}
      {hasUnsavedChanges && (
        <Alert
          message="You have unsaved changes"
          description="Don't forget to save your tutorial settings"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Action Buttons */}
      <Divider />
      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
          >
            Reset to Defaults
          </Button>
        </Space>
        
        <Space>
          <Button onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="primary"
            icon={<CheckCircleOutlined />}
            onClick={handleSave}
            disabled={!hasUnsavedChanges}
          >
            Save Settings
          </Button>
        </Space>
      </Space>
    </SettingsContainer>
  );
};

export default TutorialSettingsPanel;
