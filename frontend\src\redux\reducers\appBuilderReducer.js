/**
 * App Builder Reducer
 * 
 * Unified reducer for App Builder features integration
 * Handles components, layouts, themes, and export functionality
 */

import * as types from '../actions/types';

const initialState = {
  // Project state
  projects: [],
  activeProject: null,
  
  // Components state
  components: [],
  selectedComponent: null,
  draggedComponent: null,
  
  // Layouts state
  layouts: [],
  selectedLayout: null,
  
  // Themes state
  themes: [],
  activeTheme: null,
  
  // Export state
  exportSettings: {
    framework: 'react',
    typescript: true,
    includeTests: false,
    includeStyles: true,
    codeQuality: {
      prettier: true,
      eslint: true,
      components: true
    }
  },
  exportHistory: [],
  
  // UI state
  activeFeature: 'components',
  previewMode: false,
  
  // History for undo/redo
  history: {
    past: [],
    present: null,
    future: []
  },
  
  // Loading and error states
  loading: false,
  error: null
};

const appBuilderReducer = (state = initialState, action) => {
  switch (action.type) {
    // Component actions
    case types.ADD_COMPONENT:
      return {
        ...state,
        components: [...state.components, action.payload],
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.UPDATE_COMPONENT:
      return {
        ...state,
        components: state.components.map(component =>
          component.id === action.payload.id
            ? { ...component, ...action.payload }
            : component
        ),
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.DELETE_COMPONENT:
      return {
        ...state,
        components: state.components.filter(component => component.id !== action.payload.id),
        selectedComponent: state.selectedComponent?.id === action.payload.id ? null : state.selectedComponent,
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.DUPLICATE_COMPONENT:
      const componentToDuplicate = state.components.find(c => c.id === action.payload.id);
      if (!componentToDuplicate) return state;
      
      const duplicatedComponent = {
        ...componentToDuplicate,
        id: `${componentToDuplicate.id}_copy_${Date.now()}`,
        name: `${componentToDuplicate.name} (Copy)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return {
        ...state,
        components: [...state.components, duplicatedComponent],
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    // Layout actions
    case types.ADD_LAYOUT:
      return {
        ...state,
        layouts: [...state.layouts, action.payload],
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.UPDATE_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.id
            ? { ...layout, ...action.payload }
            : layout
        ),
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.DELETE_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.filter(layout => layout.id !== action.payload.id),
        selectedLayout: state.selectedLayout?.id === action.payload.id ? null : state.selectedLayout,
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.ADD_COMPONENT_TO_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                items: [
                  ...(layout.items || []),
                  {
                    id: action.payload.id,
                    componentId: action.payload.componentId,
                    position: action.payload.position
                  }
                ],
                updatedAt: new Date().toISOString()
              }
            : layout
        ),
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.REMOVE_COMPONENT_FROM_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                items: (layout.items || []).filter(item => item.id !== action.payload.layoutItemId),
                updatedAt: new Date().toISOString()
              }
            : layout
        ),
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    case types.MOVE_COMPONENT_IN_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                items: (layout.items || []).map(item =>
                  item.id === action.payload.layoutItemId
                    ? { ...item, position: action.payload.position }
                    : item
                ),
                updatedAt: new Date().toISOString()
              }
            : layout
        ),
        history: {
          ...state.history,
          past: [...state.history.past, state],
          future: []
        }
      };
      
    // Theme actions
    case types.ADD_THEME:
      return {
        ...state,
        themes: [...state.themes, action.payload]
      };
      
    case types.UPDATE_THEME:
      return {
        ...state,
        themes: state.themes.map(theme =>
          theme.id === action.payload.id
            ? { ...theme, ...action.payload }
            : theme
        )
      };
      
    case types.DELETE_THEME:
      return {
        ...state,
        themes: state.themes.filter(theme => theme.id !== action.payload.id),
        activeTheme: state.activeTheme === action.payload.id ? null : state.activeTheme
      };
      
    case types.SET_ACTIVE_THEME:
      return {
        ...state,
        activeTheme: action.payload.themeId
      };
      
    case types.APPLY_THEME_TO_COMPONENT:
      return {
        ...state,
        components: state.components.map(component =>
          component.id === action.payload.componentId
            ? { ...component, themeId: action.payload.themeId, updatedAt: new Date().toISOString() }
            : component
        )
      };
      
    case types.APPLY_THEME_TO_LAYOUT:
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? { ...layout, themeId: action.payload.themeId, updatedAt: new Date().toISOString() }
            : layout
        )
      };
      
    // Project actions
    case types.CREATE_PROJECT:
      return {
        ...state,
        projects: [...state.projects, action.payload]
      };
      
    case types.UPDATE_PROJECT:
      return {
        ...state,
        projects: state.projects.map(project =>
          project.id === action.payload.id
            ? { ...project, ...action.payload }
            : project
        )
      };
      
    case types.SET_ACTIVE_PROJECT:
      const activeProject = state.projects.find(p => p.id === action.payload.projectId);
      return {
        ...state,
        activeProject: action.payload.projectId,
        components: activeProject?.components || [],
        layouts: activeProject?.layouts || [],
        themes: activeProject?.themes || [],
        activeTheme: activeProject?.activeTheme || null
      };
      
    // Export actions
    case types.SET_EXPORT_SETTINGS:
      return {
        ...state,
        exportSettings: { ...state.exportSettings, ...action.payload }
      };
      
    case types.EXPORT_PROJECT_SUCCESS:
      return {
        ...state,
        exportHistory: [action.payload, ...state.exportHistory.slice(0, 9)] // Keep last 10 exports
      };
      
    // UI state actions
    case types.SET_ACTIVE_FEATURE:
      return {
        ...state,
        activeFeature: action.payload.feature
      };
      
    case types.SET_SELECTED_COMPONENT:
      return {
        ...state,
        selectedComponent: action.payload.componentId
          ? state.components.find(c => c.id === action.payload.componentId)
          : null
      };
      
    case types.SET_SELECTED_LAYOUT:
      return {
        ...state,
        selectedLayout: action.payload.layoutId
          ? state.layouts.find(l => l.id === action.payload.layoutId)
          : null
      };
      
    case types.SET_PREVIEW_MODE:
      return {
        ...state,
        previewMode: action.payload.enabled
      };
      
    case types.SET_DRAGGED_COMPONENT:
      return {
        ...state,
        draggedComponent: action.payload.component
      };
      
    case types.CLEAR_DRAGGED_COMPONENT:
      return {
        ...state,
        draggedComponent: null
      };
      
    // History actions
    case types.UNDO:
      if (state.history.past.length === 0) return state;
      
      const previous = state.history.past[state.history.past.length - 1];
      const newPast = state.history.past.slice(0, state.history.past.length - 1);
      
      return {
        ...previous,
        history: {
          past: newPast,
          present: state,
          future: [state, ...state.history.future]
        }
      };
      
    case types.REDO:
      if (state.history.future.length === 0) return state;
      
      const next = state.history.future[0];
      const newFuture = state.history.future.slice(1);
      
      return {
        ...next,
        history: {
          past: [...state.history.past, state],
          present: next,
          future: newFuture
        }
      };
      
    case types.CLEAR_HISTORY:
      return {
        ...state,
        history: {
          past: [],
          present: null,
          future: []
        }
      };
      
    default:
      return state;
  }
};

export default appBuilderReducer;
