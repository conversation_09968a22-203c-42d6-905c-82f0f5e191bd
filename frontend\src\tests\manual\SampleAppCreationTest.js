/**
 * Manual Test Script for Sample App Creation Functionality
 * 
 * This script provides comprehensive testing for the App Builder's sample app creation features.
 * It tests both the LightweightAppBuilder and attempts to test IntegratedAppBuilder functionality.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import LightweightAppBuilder from '../../pages/LightweightAppBuilder';

// Test utilities
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Test Suite for LightweightAppBuilder Sample Functionality
 */
describe('LightweightAppBuilder Sample App Creation', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
  });

  describe('Tutorial System Tests', () => {
    test('should start interactive tutorial', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to Tutorial tab
      const tutorialTab = screen.getByText('Tutorial');
      await user.click(tutorialTab);
      
      // Find and click start tutorial button
      const startButton = screen.getByText('Start Interactive Tutorial');
      expect(startButton).toBeInTheDocument();
      
      await user.click(startButton);
      
      // Verify tutorial started
      await waitFor(() => {
        expect(screen.getByText('Tutorial Step 1 of 5')).toBeInTheDocument();
        expect(screen.getByText("Welcome! Let's start by creating your first component.")).toBeInTheDocument();
      });
    });

    test('should progress through tutorial steps', async () => {
      render(<LightweightAppBuilder />);
      
      // Start tutorial
      const tutorialTab = screen.getByText('Tutorial');
      await user.click(tutorialTab);
      
      const startButton = screen.getByText('Start Interactive Tutorial');
      await user.click(startButton);
      
      // Progress through steps
      const nextButton = screen.getByText('Next Step');
      
      // Step 1 -> 2
      await user.click(nextButton);
      await waitFor(() => {
        expect(screen.getByText('Tutorial Step 2 of 5')).toBeInTheDocument();
        expect(screen.getByText("Great! Now drag your component to the layout canvas.")).toBeInTheDocument();
      });
      
      // Step 2 -> 3
      await user.click(nextButton);
      await waitFor(() => {
        expect(screen.getByText('Tutorial Step 3 of 5')).toBeInTheDocument();
        expect(screen.getByText("Perfect! Let's customize the theme colors.")).toBeInTheDocument();
      });
    });

    test('should allow skipping tutorial', async () => {
      render(<LightweightAppBuilder />);
      
      // Start tutorial
      const tutorialTab = screen.getByText('Tutorial');
      await user.click(tutorialTab);
      
      const startButton = screen.getByText('Start Interactive Tutorial');
      await user.click(startButton);
      
      // Skip tutorial
      const skipButton = screen.getByText('Skip Tutorial');
      await user.click(skipButton);
      
      // Verify tutorial ended
      await waitFor(() => {
        expect(screen.queryByText('Tutorial Step')).not.toBeInTheDocument();
        expect(screen.getByText('Start Interactive Tutorial')).toBeInTheDocument();
      });
    });
  });

  describe('Component Creation Tests', () => {
    test('should create a new component', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to Component Builder tab
      const componentTab = screen.getByText('Component Builder');
      await user.click(componentTab);
      
      // Fill in component details
      const nameInput = screen.getByPlaceholderText('Component name');
      await user.type(nameInput, 'TestButton');
      
      const typeSelect = screen.getByDisplayValue('button');
      expect(typeSelect).toBeInTheDocument();
      
      // Create component
      const createButton = screen.getByText('Create Component');
      await user.click(createButton);
      
      // Verify component was created
      await waitFor(() => {
        expect(screen.getByText('TestButton')).toBeInTheDocument();
      });
    });

    test('should create multiple component types', async () => {
      render(<LightweightAppBuilder />);
      
      const componentTab = screen.getByText('Component Builder');
      await user.click(componentTab);
      
      const componentTypes = ['button', 'input', 'text', 'card'];
      
      for (const type of componentTypes) {
        const nameInput = screen.getByPlaceholderText('Component name');
        await user.clear(nameInput);
        await user.type(nameInput, `Test${type.charAt(0).toUpperCase() + type.slice(1)}`);
        
        const typeSelect = screen.getByDisplayValue(componentTypes[0]);
        await user.selectOptions(typeSelect, type);
        
        const createButton = screen.getByText('Create Component');
        await user.click(createButton);
        
        await delay(100); // Small delay between creations
      }
      
      // Verify all components were created
      await waitFor(() => {
        expect(screen.getByText('TestButton')).toBeInTheDocument();
        expect(screen.getByText('TestInput')).toBeInTheDocument();
        expect(screen.getByText('TestText')).toBeInTheDocument();
        expect(screen.getByText('TestCard')).toBeInTheDocument();
      });
    });
  });

  describe('Layout Designer Tests', () => {
    test('should create a layout', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to Layout Designer tab
      const layoutTab = screen.getByText('Layout Designer');
      await user.click(layoutTab);
      
      // Create a layout
      const nameInput = screen.getByPlaceholderText('Layout name');
      await user.type(nameInput, 'TestLayout');
      
      const createButton = screen.getByText('Create Layout');
      await user.click(createButton);
      
      // Verify layout was created
      await waitFor(() => {
        expect(screen.getByText('TestLayout')).toBeInTheDocument();
      });
    });
  });

  describe('Theme Manager Tests', () => {
    test('should apply predefined themes', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to Theme Manager tab
      const themeTab = screen.getByText('Theme Manager');
      await user.click(themeTab);
      
      // Apply dark theme
      const darkThemeButton = screen.getByText('Dark Mode');
      await user.click(darkThemeButton);
      
      // Verify theme preview updated
      await waitFor(() => {
        const preview = screen.getByText('Sample Button');
        expect(preview).toBeInTheDocument();
      });
    });

    test('should customize theme colors', async () => {
      render(<LightweightAppBuilder />);
      
      const themeTab = screen.getByText('Theme Manager');
      await user.click(themeTab);
      
      // Find color inputs and update them
      const colorInputs = screen.getAllByDisplayValue('#1890ff');
      if (colorInputs.length > 0) {
        await user.clear(colorInputs[0]);
        await user.type(colorInputs[0], '#ff4d4f');
      }
      
      // Verify preview updates
      await delay(500);
      const preview = screen.getByText('Sample Button');
      expect(preview).toBeInTheDocument();
    });
  });

  describe('Testing Tools Tests', () => {
    test('should run component tests', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to Testing tab
      const testingTab = screen.getByText('Testing');
      await user.click(testingTab);
      
      // Run tests
      const runTestsButton = screen.getByText('Run Component Tests');
      await user.click(runTestsButton);
      
      // Verify test results appear
      await waitFor(() => {
        expect(screen.getByText('Test Results')).toBeInTheDocument();
      }, { timeout: 3000 });
    });
  });

  describe('WebSocket Manager Tests', () => {
    test('should display WebSocket connection status', async () => {
      render(<LightweightAppBuilder />);
      
      // Navigate to WebSocket tab
      const websocketTab = screen.getByText('WebSocket');
      await user.click(websocketTab);
      
      // Verify connection controls are present
      expect(screen.getByText('Connect to WebSocket')).toBeInTheDocument();
      expect(screen.getByText('Connection Status')).toBeInTheDocument();
    });
  });

  describe('Code Export Tests', () => {
    test('should generate code export', async () => {
      render(<LightweightAppBuilder />);
      
      // Create a component first
      const componentTab = screen.getByText('Component Builder');
      await user.click(componentTab);
      
      const nameInput = screen.getByPlaceholderText('Component name');
      await user.type(nameInput, 'ExportTest');
      
      const createButton = screen.getByText('Create Component');
      await user.click(createButton);
      
      // Navigate to Export tab
      const exportTab = screen.getByText('Export');
      await user.click(exportTab);
      
      // Generate code
      const generateButton = screen.getByText('Generate Code');
      await user.click(generateButton);
      
      // Verify code was generated
      await waitFor(() => {
        const codeArea = screen.getByDisplayValue(/import React/);
        expect(codeArea).toBeInTheDocument();
      });
    });
  });
});

/**
 * Integration Test for Complete Workflow
 */
describe('Complete App Building Workflow', () => {
  test('should complete end-to-end app creation workflow', async () => {
    const user = userEvent.setup();
    render(<LightweightAppBuilder />);
    
    // Step 1: Create components
    const componentTab = screen.getByText('Component Builder');
    await user.click(componentTab);
    
    // Create button component
    let nameInput = screen.getByPlaceholderText('Component name');
    await user.type(nameInput, 'WelcomeButton');
    let createButton = screen.getByText('Create Component');
    await user.click(createButton);
    
    // Create text component
    await user.clear(nameInput);
    await user.type(nameInput, 'WelcomeText');
    const typeSelect = screen.getByDisplayValue('button');
    await user.selectOptions(typeSelect, 'text');
    await user.click(createButton);
    
    // Step 2: Create layout
    const layoutTab = screen.getByText('Layout Designer');
    await user.click(layoutTab);
    
    const layoutNameInput = screen.getByPlaceholderText('Layout name');
    await user.type(layoutNameInput, 'WelcomeLayout');
    const createLayoutButton = screen.getByText('Create Layout');
    await user.click(createLayoutButton);
    
    // Step 3: Apply theme
    const themeTab = screen.getByText('Theme Manager');
    await user.click(themeTab);
    
    const modernThemeButton = screen.getByText('Modern');
    await user.click(modernThemeButton);
    
    // Step 4: Export code
    const exportTab = screen.getByText('Export');
    await user.click(exportTab);
    
    const generateButton = screen.getByText('Generate Code');
    await user.click(generateButton);
    
    // Verify complete workflow
    await waitFor(() => {
      expect(screen.getByDisplayValue(/import React/)).toBeInTheDocument();
    });
  });
});

export default {
  testSuites: [
    'LightweightAppBuilder Sample App Creation',
    'Complete App Building Workflow'
  ],
  description: 'Comprehensive test suite for App Builder sample app creation functionality'
};
