<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle Loading Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>App Builder Bundle Loading Debug</h1>
        
        <div id="status-container">
            <div class="status info">
                <strong>Checking bundle loading status...</strong>
            </div>
        </div>

        <div>
            <button onclick="checkMainApp()">Check Main App</button>
            <button onclick="checkAppBuilder()">Check App Builder</button>
            <button onclick="checkComponents()">Check Components</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div id="logs">
            <h3>Debug Logs</h3>
            <pre id="log-output"></pre>
        </div>
    </div>

    <script>
        let logOutput = document.getElementById('log-output');
        let statusContainer = document.getElementById('status-container');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function addStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>${message}</strong>`;
            statusContainer.appendChild(statusDiv);
        }

        function clearLogs() {
            logOutput.textContent = '';
            statusContainer.innerHTML = '<div class="status info"><strong>Logs cleared</strong></div>';
        }

        function checkMainApp() {
            log('Checking main application...');
            
            try {
                // Check if React is loaded
                if (typeof React !== 'undefined') {
                    log('✓ React is loaded', 'success');
                    addStatus('React: Loaded', 'success');
                } else {
                    log('✗ React is not loaded', 'error');
                    addStatus('React: Not Loaded', 'error');
                }

                // Check if the main app is running
                fetch('http://localhost:3000')
                    .then(response => {
                        if (response.ok) {
                            log('✓ Main app is responding', 'success');
                            addStatus('Main App: Responding', 'success');
                        } else {
                            log('✗ Main app returned error: ' + response.status, 'error');
                            addStatus('Main App: Error ' + response.status, 'error');
                        }
                    })
                    .catch(error => {
                        log('✗ Main app connection failed: ' + error.message, 'error');
                        addStatus('Main App: Connection Failed', 'error');
                    });

            } catch (error) {
                log('✗ Error checking main app: ' + error.message, 'error');
                addStatus('Main App: Error', 'error');
            }
        }

        function checkAppBuilder() {
            log('Checking App Builder components...');
            
            // Check if we can access the app builder route
            fetch('http://localhost:3000/app-builder')
                .then(response => {
                    if (response.ok) {
                        log('✓ App Builder route is accessible', 'success');
                        addStatus('App Builder Route: Accessible', 'success');
                    } else {
                        log('✗ App Builder route error: ' + response.status, 'error');
                        addStatus('App Builder Route: Error ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    log('✗ App Builder route failed: ' + error.message, 'error');
                    addStatus('App Builder Route: Failed', 'error');
                });
        }

        function checkComponents() {
            log('Checking component availability...');
            
            const components = [
                'TestingTools',
                'DataManagementTools', 
                'EnhancedCodeExporter',
                'WebSocketManager',
                'IntegratedTutorialAssistant'
            ];

            components.forEach(component => {
                try {
                    log(`Checking ${component}...`);
                    // This is a basic check - in a real scenario, we'd check if the components are actually loaded
                    log(`✓ ${component} check completed`, 'info');
                } catch (error) {
                    log(`✗ ${component} error: ${error.message}`, 'error');
                }
            });

            addStatus('Component Check: Completed', 'info');
        }

        // Check browser console errors
        window.addEventListener('error', function(e) {
            log('Browser Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno, 'error');
            addStatus('Browser Error Detected', 'error');
        });

        // Check unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            log('Unhandled Promise Rejection: ' + e.reason, 'error');
            addStatus('Promise Rejection Detected', 'error');
        });

        // Initial status check
        log('Debug page loaded successfully');
        addStatus('Debug Page: Loaded', 'success');
        
        // Auto-check main app on load
        setTimeout(checkMainApp, 1000);
    </script>
</body>
</html>
