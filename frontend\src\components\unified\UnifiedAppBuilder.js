/**
 * Unified App Builder Interface
 * 
 * Integrates all App Builder features into a cohesive workflow
 * Provides seamless navigation between Component Builder, Layout Designer, Theme Manager, and Export
 */

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Layout,
  Tabs,
  Button,
  Space,
  Typography,
  Progress,
  Badge,
  Tooltip,
  message
} from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  ExportOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  ShareAltOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

// Import unified actions and selectors
import {
  setActiveFeature,
  setPreviewMode,
  createProject,
  setActiveProject
} from '../../redux/actions/appBuilderActions';
import {
  getComponents,
  getLayouts,
  getThemes,
  getActiveFeature,
  getPreviewMode,
  getCompleteProjectData,
  getValidationErrors,
  isProjectValid
} from '../../redux/selectors/appBuilderSelectors';

// Import enhanced drag-and-drop system
import { DragDropProvider } from '../shared/DragDropSystem';

// Import feature components
import ComponentBuilder from '../enhanced/ComponentBuilder';
import LayoutDesigner from '../enhanced/LayoutDesigner';
import ThemeManager from '../enhanced/ThemeManager';
import IntegratedExportSystem from '../export/IntegratedExportSystem';

// Import theme application system
import { ThemeApplicationProvider } from '../theme/ThemeApplicationSystem';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

const UnifiedAppBuilder = ({ projectId, initialData = {} }) => {
  const dispatch = useDispatch();

  // Redux state
  const components = useSelector(getComponents);
  const layouts = useSelector(getLayouts);
  const themes = useSelector(getThemes);
  const activeFeature = useSelector(getActiveFeature);
  const previewMode = useSelector(getPreviewMode);
  const projectData = useSelector(getCompleteProjectData);
  const validationErrors = useSelector(getValidationErrors);
  const isValid = useSelector(isProjectValid);

  // Local state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [workflowStep, setWorkflowStep] = useState(0);
  const [showWorkflowGuide, setShowWorkflowGuide] = useState(true);

  // Initialize project if needed
  useEffect(() => {
    if (projectId && !projectData.project) {
      dispatch(createProject({
        id: projectId,
        name: 'New App Builder Project',
        description: 'Created with unified App Builder',
        ...initialData
      }));
      dispatch(setActiveProject(projectId));
    }
  }, [projectId, projectData.project, dispatch, initialData]);

  // Workflow steps
  const workflowSteps = [
    {
      key: 'components',
      title: 'Create Components',
      description: 'Build reusable UI components',
      icon: <AppstoreOutlined />,
      completed: components.length > 0,
      component: ComponentBuilder
    },
    {
      key: 'layouts',
      title: 'Design Layouts',
      description: 'Arrange components in layouts',
      icon: <LayoutOutlined />,
      completed: layouts.length > 0,
      component: LayoutDesigner,
      disabled: components.length === 0
    },
    {
      key: 'themes',
      title: 'Apply Themes',
      description: 'Customize colors and styling',
      icon: <BgColorsOutlined />,
      completed: themes.length > 0,
      component: ThemeManager
    },
    {
      key: 'export',
      title: 'Export App',
      description: 'Generate final application code',
      icon: <ExportOutlined />,
      completed: false,
      component: () => <IntegratedExportSystem projectId={projectId} />,
      disabled: !isValid
    }
  ];

  // Calculate workflow progress
  const completedSteps = workflowSteps.filter(step => step.completed).length;
  const workflowProgress = (completedSteps / workflowSteps.length) * 100;

  // Handle feature change
  const handleFeatureChange = (featureKey) => {
    dispatch(setActiveFeature(featureKey));

    // Update workflow step
    const stepIndex = workflowSteps.findIndex(step => step.key === featureKey);
    if (stepIndex !== -1) {
      setWorkflowStep(stepIndex);
    }
  };

  // Handle preview toggle
  const handlePreviewToggle = () => {
    dispatch(setPreviewMode(!previewMode));
  };

  // Handle save project
  const handleSaveProject = () => {
    // TODO: Implement project saving
    message.success('Project saved successfully');
  };

  // Handle export
  const handleExport = () => {
    if (!isValid) {
      message.error('Please fix validation errors before exporting');
      return;
    }
    // TODO: Implement export functionality
    message.success('Export started');
  };

  // Get current feature component
  const getCurrentFeatureComponent = () => {
    const currentStep = workflowSteps.find(step => step.key === activeFeature);
    if (currentStep && currentStep.component) {
      const FeatureComponent = currentStep.component;
      return <FeatureComponent />;
    }
    return <ComponentBuilder />; // Default fallback
  };

  // Workflow guide component
  const WorkflowGuide = () => (
    <div style={{
      padding: '16px',
      backgroundColor: '#f0f9ff',
      border: '1px solid #bae6fd',
      borderRadius: '8px',
      marginBottom: '16px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
        <Title level={2} style={{ margin: 0, fontSize: '18px' }}>
          <InfoCircleOutlined style={{ marginRight: '8px', color: '#0ea5e9' }} />
          App Builder Workflow
        </Title>
        <Button
          type="text"
          size="small"
          onClick={() => setShowWorkflowGuide(false)}
          aria-label="Close workflow guide"
        >
          ×
        </Button>
      </div>

      <Progress
        percent={workflowProgress}
        size="small"
        status={workflowProgress === 100 ? 'success' : 'active'}
        style={{ marginBottom: '12px' }}
      />

      <Text type="secondary">
        Step {workflowStep + 1} of {workflowSteps.length}: {workflowSteps[workflowStep]?.description}
      </Text>
    </div>
  );

  // Project stats component
  const ProjectStats = () => (
    <div style={{ padding: '16px', backgroundColor: '#fafafa', borderRadius: '8px' }}>
      <Title level={3} style={{ fontSize: '16px' }}>Project Overview</Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text>Components:</Text>
          <Badge count={components.length} showZero />
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text>Layouts:</Text>
          <Badge count={layouts.length} showZero />
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text>Themes:</Text>
          <Badge count={themes.length} showZero />
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text>Validation:</Text>
          {isValid ? (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          ) : (
            <Badge count={validationErrors.length} />
          )}
        </div>
      </Space>
    </div>
  );

  // Tab items for the main interface
  const tabItems = workflowSteps.map(step => ({
    key: step.key,
    label: (
      <Space>
        {step.icon}
        <span>{step.title}</span>
        {step.completed && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
      </Space>
    ),
    children: getCurrentFeatureComponent(),
    disabled: step.disabled
  }));

  return (
    <ThemeApplicationProvider>
      <DragDropProvider>
        <Layout style={{ minHeight: '100vh' }} role="application" aria-label="App Builder application">
          {/* Header */}
          <Header style={{
            background: '#fff',
            padding: '0 24px',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }} role="banner" aria-label="App Builder header">
            <div>
              <Title level={1} style={{ margin: 0, fontSize: '24px' }}>
                App Builder
              </Title>
              <Text type="secondary">
                {projectData.project?.name || 'Unified Development Environment'}
              </Text>
            </div>

            <Space>
              <Tooltip title="Preview Mode">
                <Button
                  type={previewMode ? 'primary' : 'default'}
                  icon={<PlayCircleOutlined />}
                  onClick={handlePreviewToggle}
                >
                  Preview
                </Button>
              </Tooltip>

              <Button
                icon={<SaveOutlined />}
                onClick={handleSaveProject}
                aria-label="Save project"
              >
                Save
              </Button>

              <Button
                icon={<ShareAltOutlined />}
                onClick={() => message.info('Share functionality coming soon')}
                aria-label="Share project"
              >
                Share
              </Button>

              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={handleExport}
                disabled={!isValid}
                aria-label="Export application"
              >
                Export
              </Button>

              <Button
                icon={<SettingOutlined />}
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                aria-label="Toggle sidebar"
              />
            </Space>
          </Header>

          <Layout>
            {/* Sidebar */}
            <Sider
              width={300}
              collapsible
              collapsed={sidebarCollapsed}
              onCollapse={setSidebarCollapsed}
              style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}
              aria-label="Project sidebar"
            >
              <div style={{ padding: '16px' }}>
                {!sidebarCollapsed && (
                  <>
                    {showWorkflowGuide && <WorkflowGuide />}
                    <ProjectStats />
                  </>
                )}
              </div>
            </Sider>

            {/* Main Content */}
            <Content style={{ background: '#fff' }} role="main" aria-label="App Builder main content">
              <Tabs
                activeKey={activeFeature}
                onChange={handleFeatureChange}
                type="card"
                size="large"
                style={{ height: '100%' }}
                items={tabItems}
                aria-label="App Builder feature tabs"
              />
            </Content>
          </Layout>
        </Layout>
      </DragDropProvider>
    </ThemeApplicationProvider>
  );
};

// Export Panel Component (placeholder)
const ExportPanel = () => (
  <div style={{ padding: '24px', textAlign: 'center' }}>
    <Title level={3}>Export Your Application</Title>
    <Text>Export functionality will be implemented in the next phase.</Text>
  </div>
);

export default UnifiedAppBuilder;
