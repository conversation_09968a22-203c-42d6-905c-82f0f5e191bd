const WebSocket = require('ws');

console.log('Testing WebSocket connection through frontend proxy...');

const ws = new WebSocket('ws://localhost:3000/api/ws');

ws.on('open', function open() {
  console.log('✅ WebSocket connection established through proxy');

  // Send a test message
  const testMessage = {
    type: 'ping',
    timestamp: new Date().toISOString(),
    message: 'Hello from proxy test client'
  };

  ws.send(JSON.stringify(testMessage));
  console.log('📤 Sent test message through proxy:', testMessage);
});

ws.on('message', function message(data) {
  console.log('📥 Received through proxy:', data.toString());
});

ws.on('error', function error(err) {
  console.log('❌ WebSocket proxy error:', err.message);
});

ws.on('close', function close() {
  console.log('🔌 WebSocket proxy connection closed');
});

// Close connection after 3 seconds
setTimeout(() => {
  console.log('⏰ Closing proxy connection...');
  ws.close();
}, 3000);
