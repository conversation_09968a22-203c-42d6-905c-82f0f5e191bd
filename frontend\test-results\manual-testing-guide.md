# Manual Testing Guide: App Builder Sample App Creation

This guide provides step-by-step instructions for manually testing the App Builder's sample app creation functionality.

## Prerequisites

1. **Start the Application**
   ```bash
   # Terminal 1: Start Backend
   cd backend
   python manage.py runserver 0.0.0.0:8000

   # Terminal 2: Start Frontend
   cd frontend
   npm start
   ```

2. **Open Browser**
   - Navigate to: `http://localhost:3000/app-builder`
   - Verify the LightweightAppBuilder interface loads

## Test Scenario 1: Complete Sample App Creation Workflow

### Step 1: Create Components (Component Builder Tab)

1. **Navigate to Component Builder**
   - Click the "Component Builder" tab
   - Verify the component creation interface appears

2. **Create a Welcome Button**
   - Component Name: `WelcomeButton`
   - Component Type: `button` (default)
   - Props: `{"text": "Welcome to My App", "type": "primary"}`
   - Click "Create Component"
   - ✅ **Expected**: Component appears in the components list

3. **Create a Title Text**
   - Component Name: `AppTitle`
   - Component Type: `text`
   - Props: `{"text": "My Sample Application", "size": "large"}`
   - Click "Create Component"
   - ✅ **Expected**: Component appears in the components list

4. **Create an Input Field**
   - Component Name: `UserInput`
   - Component Type: `input`
   - Props: `{"placeholder": "Enter your name"}`
   - Click "Create Component"
   - ✅ **Expected**: Component appears in the components list

5. **Create a Card Component**
   - Component Name: `InfoCard`
   - Component Type: `card`
   - Props: `{"title": "Information", "content": "This is a sample card component"}`
   - Click "Create Component"
   - ✅ **Expected**: Component appears in the components list

### Step 2: Design Layout (Layout Designer Tab)

1. **Navigate to Layout Designer**
   - Click the "Layout Designer" tab
   - Verify the drag-and-drop canvas appears

2. **Drag Components to Canvas**
   - Drag `AppTitle` from the components list to the canvas
   - Drag `WelcomeButton` to the canvas
   - Drag `UserInput` to the canvas
   - Drag `InfoCard` to the canvas
   - ✅ **Expected**: Components appear positioned on the canvas

3. **Create Layout**
   - Layout Name: `SampleAppLayout`
   - Click "Create Layout"
   - ✅ **Expected**: Layout appears in the layouts list

### Step 3: Apply Theme (Theme Manager Tab)

1. **Navigate to Theme Manager**
   - Click the "Theme Manager" tab
   - Verify theme options and preview appear

2. **Try Predefined Themes**
   - Click "Dark Mode" theme
   - ✅ **Expected**: Preview updates with dark theme
   - Click "Modern" theme
   - ✅ **Expected**: Preview updates with modern theme

3. **Customize Theme**
   - Primary Color: Change to `#52c41a` (green)
   - Background Color: Change to `#f6ffed` (light green)
   - Text Color: Keep as `#000000`
   - Border Radius: Change to `8px`
   - ✅ **Expected**: Preview updates with custom colors

4. **Save Custom Theme**
   - Click "Save Theme"
   - ✅ **Expected**: Custom theme appears in themes list

### Step 4: Export Code (Export Tab)

1. **Navigate to Export**
   - Click the "Export" tab
   - Verify export options appear

2. **Generate React Code**
   - Framework: `React` (default)
   - Format: `JSX`
   - Click "Generate Code"
   - ✅ **Expected**: Complete React code appears in the text area

3. **Test Different Formats**
   - Change Format to `TSX`
   - Click "Generate Code"
   - ✅ **Expected**: TypeScript React code generated
   - Change Framework to `Vue`
   - Click "Generate Code"
   - ✅ **Expected**: Vue.js code generated

## Test Scenario 2: Tutorial System

### Step 1: Start Tutorial

1. **Navigate to Tutorial**
   - Click the "Tutorial" tab
   - Verify tutorial controls appear

2. **Start Interactive Tutorial**
   - Click "Start Interactive Tutorial"
   - ✅ **Expected**: Tutorial Step 1 of 5 appears
   - ✅ **Expected**: Welcome message displays

### Step 2: Progress Through Tutorial

1. **Step Through Tutorial**
   - Click "Next Step" button
   - ✅ **Expected**: Step 2 of 5 appears with drag-drop message
   - Click "Next Step" again
   - ✅ **Expected**: Step 3 of 5 appears with theme message
   - Continue through all steps
   - ✅ **Expected**: Each step shows appropriate guidance

2. **Test Skip Functionality**
   - Start tutorial again
   - Click "Skip Tutorial"
   - ✅ **Expected**: Tutorial ends and returns to start state

## Test Scenario 3: Testing Tools

### Step 1: Run Component Tests

1. **Navigate to Testing**
   - Click the "Testing" tab
   - Verify testing interface appears

2. **Run Tests**
   - Click "Run Component Tests"
   - ✅ **Expected**: Button shows loading state
   - Wait for completion (2 seconds)
   - ✅ **Expected**: Test results appear in the results panel
   - ✅ **Expected**: Pass/fail status shown for each component

### Step 2: Verify Test Coverage

1. **Check Coverage Metrics**
   - ✅ **Expected**: Accessibility coverage shown (85%)
   - ✅ **Expected**: Layout validation shown (92%)
   - ✅ **Expected**: Component tests shown (78%)

## Test Scenario 4: WebSocket Functionality

### Step 1: Test Connection

1. **Navigate to WebSocket**
   - Click the "WebSocket" tab
   - Verify connection interface appears

2. **Connect to WebSocket**
   - Click "Connect to WebSocket"
   - ✅ **Expected**: Connection status updates
   - ✅ **Expected**: Connection message appears in message list

### Step 2: Test Collaboration Features

1. **Check Collaboration Status**
   - ✅ **Expected**: Collaborators list appears
   - ✅ **Expected**: Real-time status indicators present

## Test Scenario 5: Data Management

### Step 1: Create State Variables

1. **Navigate to Data**
   - Click the "Data" tab
   - Verify data management interface appears

2. **Add State Variables**
   - Type `userName` in the variable input
   - Press Enter
   - ✅ **Expected**: Variable appears in the state variables list
   - Add another variable: `isLoggedIn`
   - ✅ **Expected**: Both variables listed

### Step 2: Bind Data to Components

1. **Create Data Bindings**
   - Select a component from the bindings list
   - Choose a state variable from the dropdown
   - ✅ **Expected**: Binding is created and displayed

## Test Scenario 6: Performance Monitoring

### Step 1: Check Performance Metrics

1. **Navigate to Performance**
   - Click the "Performance" tab
   - Verify performance dashboard appears

2. **Review Metrics**
   - ✅ **Expected**: Bundle size displayed (should be ~2.34MB)
   - ✅ **Expected**: Render time shown
   - ✅ **Expected**: Memory usage displayed
   - ✅ **Expected**: Component count matches created components

3. **Check Optimization Score**
   - ✅ **Expected**: Performance grade shown (A+)
   - ✅ **Expected**: Optimization suggestions displayed

## Expected Results Summary

After completing all test scenarios, you should have:

1. **4 Components Created**: WelcomeButton, AppTitle, UserInput, InfoCard
2. **1 Layout Created**: SampleAppLayout with positioned components
3. **Custom Theme Applied**: Green color scheme with 8px border radius
4. **Generated Code**: React/Vue code for the complete application
5. **Tutorial Completed**: 5-step tutorial progression verified
6. **Tests Run**: Component validation with pass/fail results
7. **WebSocket Connected**: Real-time connection established
8. **Data Variables**: userName and isLoggedIn state variables
9. **Performance Metrics**: Bundle size and optimization data

## Troubleshooting

### Common Issues

1. **Components Not Appearing**
   - Check that component name is not empty
   - Verify props are valid JSON format
   - Refresh the page and try again

2. **Drag and Drop Not Working**
   - Ensure you're dragging from the components list
   - Try clicking and holding before dragging
   - Check that the drop zone is highlighted

3. **Code Export Empty**
   - Create at least one component first
   - Try switching frameworks and generating again
   - Check browser console for errors

4. **WebSocket Connection Failed**
   - Verify backend server is running on port 8000
   - Check that WebSocket endpoint is correct
   - Try refreshing the page

### Performance Expectations

- **Page Load**: Should load within 3 seconds
- **Component Creation**: Instant response
- **Code Generation**: Complete within 1 second
- **Test Execution**: Complete within 2 seconds
- **Bundle Size**: Should remain under 3MB

---

**Testing Time**: Approximately 20-30 minutes for complete workflow  
**Difficulty**: Beginner to Intermediate  
**Browser Compatibility**: Chrome, Firefox, Safari, Edge
