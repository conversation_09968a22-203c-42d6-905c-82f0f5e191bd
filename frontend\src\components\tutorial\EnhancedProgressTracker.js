/**
 * Enhanced Progress Tracker Component
 * 
 * Comprehensive progress tracking system with achievements, milestones,
 * and visual feedback for user advancement through App Builder features.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Progress,
  Typography,
  Space,
  Tag,
  Badge,
  Avatar,
  List,
  Tooltip,
  Button,
  Modal,
  Row,
  Col,
  Statistic,
  Timeline,
  Divider
} from 'antd';
import {
  TrophyOutlined,
  StarOutlined,
  FireOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RocketOutlined,
  BookOutlined,
  BulbOutlined,
  TargetOutlined,
  CrownOutlined,
  GiftOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;

// Styled Components
const ProgressContainer = styled.div`
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
`;

const AchievementCard = styled(Card)`
  margin-bottom: 12px;
  border-radius: 12px;
  border: 2px solid ${props =>
    props.unlocked ? '#52c41a' :
      props.inProgress ? '#1890ff' : '#f0f0f0'
  };
  background: ${props =>
    props.unlocked ? 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)' :
      props.inProgress ? 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)' :
        '#fafafa'
  };
  
  .ant-card-body {
    padding: 16px;
  }
  
  ${props => props.unlocked && `
    animation: glow 2s ease-in-out;
    
    @keyframes glow {
      0% { box-shadow: 0 0 5px rgba(82, 196, 26, 0.3); }
      50% { box-shadow: 0 0 20px rgba(82, 196, 26, 0.6); }
      100% { box-shadow: 0 0 5px rgba(82, 196, 26, 0.3); }
    }
  `}
`;

const MilestoneTimeline = styled(Timeline)`
  .ant-timeline-item-head {
    background-color: ${props => props.completed ? '#52c41a' : '#d9d9d9'};
  }
`;

// Achievement definitions
const ACHIEVEMENTS = {
  FIRST_COMPONENT: {
    id: 'first_component',
    title: 'First Steps',
    description: 'Added your first component to the canvas',
    icon: <RocketOutlined />,
    points: 10,
    category: 'beginner',
    requirement: 'Add 1 component'
  },
  COMPONENT_MASTER: {
    id: 'component_master',
    title: 'Component Master',
    description: 'Added 10 different components',
    icon: <StarOutlined />,
    points: 50,
    category: 'intermediate',
    requirement: 'Add 10 components'
  },
  LAYOUT_DESIGNER: {
    id: 'layout_designer',
    title: 'Layout Designer',
    description: 'Created your first custom layout',
    icon: <TargetOutlined />,
    points: 25,
    category: 'intermediate',
    requirement: 'Create 1 layout'
  },
  THEME_ARTIST: {
    id: 'theme_artist',
    title: 'Theme Artist',
    description: 'Applied a custom theme to your app',
    icon: <BulbOutlined />,
    points: 30,
    category: 'intermediate',
    requirement: 'Apply 1 theme'
  },
  TUTORIAL_GRADUATE: {
    id: 'tutorial_graduate',
    title: 'Tutorial Graduate',
    description: 'Completed your first tutorial',
    icon: <BookOutlined />,
    points: 20,
    category: 'beginner',
    requirement: 'Complete 1 tutorial'
  },
  WORKFLOW_EXPERT: {
    id: 'workflow_expert',
    title: 'Workflow Expert',
    description: 'Completed the full app creation workflow',
    icon: <CrownOutlined />,
    points: 100,
    category: 'advanced',
    requirement: 'Complete workflow'
  },
  COLLABORATION_CHAMPION: {
    id: 'collaboration_champion',
    title: 'Collaboration Champion',
    description: 'Collaborated with team members in real-time',
    icon: <FireOutlined />,
    points: 40,
    category: 'advanced',
    requirement: 'Use collaboration features'
  },
  TESTING_GURU: {
    id: 'testing_guru',
    title: 'Testing Guru',
    description: 'Ran comprehensive tests on your application',
    icon: <CheckCircleOutlined />,
    points: 35,
    category: 'advanced',
    requirement: 'Run tests'
  }
};

// Milestone definitions
const MILESTONES = [
  { id: 'getting_started', title: 'Getting Started', description: 'Complete the basics', points: 50 },
  { id: 'building_apps', title: 'Building Apps', description: 'Create your first app', points: 150 },
  { id: 'advanced_features', title: 'Advanced Features', description: 'Master advanced tools', points: 300 },
  { id: 'expert_level', title: 'Expert Level', description: 'Become an App Builder expert', points: 500 }
];

/**
 * Enhanced Progress Tracker Component
 */
const EnhancedProgressTracker = ({
  isVisible = false,
  onClose,
  userProgress = {},
  achievements = [],
  onAchievementClick
}) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAchievementModal, setShowAchievementModal] = useState(false);
  const [selectedAchievement, setSelectedAchievement] = useState(null);

  // Calculate total points
  const totalPoints = useMemo(() => {
    return achievements.reduce((sum, achievement) => {
      const achievementData = ACHIEVEMENTS[achievement.id];
      return sum + (achievementData?.points || 0);
    }, 0);
  }, [achievements]);

  // Calculate progress by category
  const categoryProgress = useMemo(() => {
    const categories = ['beginner', 'intermediate', 'advanced'];
    const progress = {};

    categories.forEach(category => {
      const categoryAchievements = Object.values(ACHIEVEMENTS).filter(a => a.category === category);
      const unlockedInCategory = achievements.filter(a => {
        const achievementData = ACHIEVEMENTS[a.id];
        return achievementData?.category === category;
      });

      progress[category] = {
        total: categoryAchievements.length,
        unlocked: unlockedInCategory.length,
        percentage: Math.round((unlockedInCategory.length / categoryAchievements.length) * 100)
      };
    });

    return progress;
  }, [achievements]);

  // Get current milestone
  const currentMilestone = useMemo(() => {
    for (let i = MILESTONES.length - 1; i >= 0; i--) {
      if (totalPoints >= MILESTONES[i].points) {
        return { ...MILESTONES[i], index: i };
      }
    }
    return { ...MILESTONES[0], index: 0 };
  }, [totalPoints]);

  // Get next milestone
  const nextMilestone = useMemo(() => {
    const nextIndex = currentMilestone.index + 1;
    return nextIndex < MILESTONES.length ? MILESTONES[nextIndex] : null;
  }, [currentMilestone]);

  // Filter achievements by category
  const filteredAchievements = useMemo(() => {
    return Object.values(ACHIEVEMENTS).filter(achievement =>
      selectedCategory === 'all' || achievement.category === selectedCategory
    );
  }, [selectedCategory]);

  // Check if achievement is unlocked
  const isAchievementUnlocked = useCallback((achievementId) => {
    return achievements.some(a => a.id === achievementId);
  }, [achievements]);

  // Handle achievement click
  const handleAchievementClick = useCallback((achievement) => {
    setSelectedAchievement(achievement);
    setShowAchievementModal(true);
    if (onAchievementClick) {
      onAchievementClick(achievement);
    }
  }, [onAchievementClick]);

  // Render achievement card
  const renderAchievementCard = useCallback((achievement) => {
    const isUnlocked = isAchievementUnlocked(achievement.id);
    const userAchievement = achievements.find(a => a.id === achievement.id);

    return (
      <AchievementCard
        key={achievement.id}
        size="small"
        unlocked={isUnlocked}
        onClick={() => handleAchievementClick(achievement)}
        style={{ cursor: 'pointer' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar
            size={48}
            icon={achievement.icon}
            style={{
              backgroundColor: isUnlocked ? '#52c41a' : '#d9d9d9',
              color: 'white'
            }}
          />

          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text strong style={{ fontSize: 14 }}>
                {achievement.title}
              </Text>
              <Space>
                <Tag color={achievement.category === 'beginner' ? 'green' : achievement.category === 'intermediate' ? 'orange' : 'red'}>
                  {achievement.category}
                </Tag>
                <Badge count={`+${achievement.points}`} style={{ backgroundColor: '#faad14' }} />
              </Space>
            </div>

            <Paragraph style={{ margin: 0, fontSize: 12, color: '#666' }}>
              {achievement.description}
            </Paragraph>

            <Text type="secondary" style={{ fontSize: 11 }}>
              {achievement.requirement}
            </Text>

            {isUnlocked && userAchievement?.earnedAt && (
              <div style={{ marginTop: 4 }}>
                <Text type="secondary" style={{ fontSize: 10 }}>
                  Earned: {new Date(userAchievement.earnedAt).toLocaleDateString()}
                </Text>
              </div>
            )}
          </div>
        </div>
      </AchievementCard>
    );
  }, [achievements, isAchievementUnlocked, handleAchievementClick]);

  if (!isVisible) return null;

  return (
    <Modal
      title={
        <Space>
          <TrophyOutlined />
          Progress Tracker
          <Badge count={achievements.length} showZero />
        </Space>
      }
      open={isVisible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <ProgressContainer>
        {/* Overall Stats */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="Total Points"
              value={totalPoints}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Achievements"
              value={achievements.length}
              suffix={`/ ${Object.keys(ACHIEVEMENTS).length}`}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Current Level"
              value={currentMilestone.title}
              prefix={<CrownOutlined />}
              valueStyle={{ fontSize: 14 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Progress"
              value={Math.round((achievements.length / Object.keys(ACHIEVEMENTS).length) * 100)}
              suffix="%"
              prefix={<TargetOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>

        {/* Milestone Progress */}
        <Card title="Milestone Progress" style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text strong>{currentMilestone.title}</Text>
              {nextMilestone && (
                <Text type="secondary">
                  Next: {nextMilestone.title} ({nextMilestone.points - totalPoints} points to go)
                </Text>
              )}
            </div>
            <Progress
              percent={nextMilestone ? Math.round((totalPoints / nextMilestone.points) * 100) : 100}
              strokeColor={{
                '0%': '#1890ff',
                '100%': '#52c41a',
              }}
            />
          </div>

          <Timeline>
            {MILESTONES.map((milestone, index) => (
              <Timeline.Item
                key={milestone.id}
                color={totalPoints >= milestone.points ? '#52c41a' : '#d9d9d9'}
                dot={totalPoints >= milestone.points ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
              >
                <div>
                  <Text strong>{milestone.title}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {milestone.description} ({milestone.points} points)
                  </Text>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>

        {/* Category Progress */}
        <Card title="Progress by Category" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            {Object.entries(categoryProgress).map(([category, progress]) => (
              <Col span={8} key={category}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    size={80}
                    percent={progress.percentage}
                    format={() => `${progress.unlocked}/${progress.total}`}
                    strokeColor={
                      category === 'beginner' ? '#52c41a' :
                        category === 'intermediate' ? '#faad14' : '#1890ff'
                    }
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text strong style={{ textTransform: 'capitalize' }}>
                      {category}
                    </Text>
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </Card>

        {/* Achievement Filter */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Text strong>Filter by category:</Text>
            {['all', 'beginner', 'intermediate', 'advanced'].map(category => (
              <Button
                key={category}
                size="small"
                type={selectedCategory === category ? 'primary' : 'default'}
                onClick={() => setSelectedCategory(category)}
                style={{ textTransform: 'capitalize' }}
              >
                {category}
              </Button>
            ))}
          </Space>
        </div>

        {/* Achievements List */}
        <Card title="Achievements">
          <div>
            {filteredAchievements.map(renderAchievementCard)}
          </div>
        </Card>
      </ProgressContainer>

      {/* Achievement Detail Modal */}
      <Modal
        title={selectedAchievement?.title}
        open={showAchievementModal}
        onCancel={() => setShowAchievementModal(false)}
        footer={null}
        width={400}
      >
        {selectedAchievement && (
          <div style={{ textAlign: 'center' }}>
            <Avatar
              size={80}
              icon={selectedAchievement.icon}
              style={{
                backgroundColor: isAchievementUnlocked(selectedAchievement.id) ? '#52c41a' : '#d9d9d9',
                marginBottom: 16
              }}
            />
            <Title level={4}>{selectedAchievement.title}</Title>
            <Paragraph>{selectedAchievement.description}</Paragraph>
            <Space>
              <Tag color={selectedAchievement.category === 'beginner' ? 'green' : selectedAchievement.category === 'intermediate' ? 'orange' : 'red'}>
                {selectedAchievement.category}
              </Tag>
              <Badge count={`+${selectedAchievement.points} points`} style={{ backgroundColor: '#faad14' }} />
            </Space>
            <Divider />
            <Text type="secondary">
              Requirement: {selectedAchievement.requirement}
            </Text>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default EnhancedProgressTracker;

// Achievement Notification System
export const showAchievementNotification = (achievement) => {
  const achievementData = ACHIEVEMENTS[achievement.id];
  if (!achievementData) return;

  // Create custom notification
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    width: 320px;
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    color: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    animation: slideIn 0.5s ease-out;
    cursor: pointer;
  `;

  notification.innerHTML = `
    <div style="display: flex; align-items: center; gap: 12px;">
      <div style="font-size: 24px;">🏆</div>
      <div style="flex: 1;">
        <div style="font-weight: 600; margin-bottom: 4px;">Achievement Unlocked!</div>
        <div style="font-size: 14px; opacity: 0.9;">${achievementData.title}</div>
        <div style="font-size: 12px; opacity: 0.8;">+${achievementData.points} points</div>
      </div>
      <div style="font-size: 18px; cursor: pointer;" onclick="this.parentElement.parentElement.remove()">×</div>
    </div>
  `;

  // Add animation styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);

  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.animation = 'slideIn 0.3s ease-in reverse';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);

  // Remove on click
  notification.addEventListener('click', () => {
    notification.style.animation = 'slideIn 0.3s ease-in reverse';
    setTimeout(() => notification.remove(), 300);
  });
};

// Progress tracking utilities
export const trackUserProgress = (action, data = {}) => {
  const progressData = JSON.parse(localStorage.getItem('app-builder-progress') || '{}');

  // Update progress based on action
  switch (action) {
    case 'component_added':
      progressData.componentsAdded = (progressData.componentsAdded || 0) + 1;
      break;
    case 'layout_created':
      progressData.layoutsCreated = (progressData.layoutsCreated || 0) + 1;
      break;
    case 'theme_applied':
      progressData.themesApplied = (progressData.themesApplied || 0) + 1;
      break;
    case 'tutorial_completed':
      progressData.tutorialsCompleted = (progressData.tutorialsCompleted || 0) + 1;
      break;
    case 'test_run':
      progressData.testsRun = (progressData.testsRun || 0) + 1;
      break;
    case 'collaboration_used':
      progressData.collaborationSessions = (progressData.collaborationSessions || 0) + 1;
      break;
  }

  // Save updated progress
  localStorage.setItem('app-builder-progress', JSON.stringify(progressData));

  // Check for new achievements
  checkForNewAchievements(progressData);
};

// Check for new achievements
const checkForNewAchievements = (progressData) => {
  const currentAchievements = JSON.parse(localStorage.getItem('app-builder-achievements') || '[]');
  const currentAchievementIds = new Set(currentAchievements.map(a => a.id));

  const newAchievements = [];

  // Check each achievement
  Object.entries(ACHIEVEMENTS).forEach(([id, achievement]) => {
    if (currentAchievementIds.has(id)) return;

    let shouldUnlock = false;

    switch (id) {
      case 'first_component':
        shouldUnlock = (progressData.componentsAdded || 0) >= 1;
        break;
      case 'component_master':
        shouldUnlock = (progressData.componentsAdded || 0) >= 10;
        break;
      case 'layout_designer':
        shouldUnlock = (progressData.layoutsCreated || 0) >= 1;
        break;
      case 'theme_artist':
        shouldUnlock = (progressData.themesApplied || 0) >= 1;
        break;
      case 'tutorial_graduate':
        shouldUnlock = (progressData.tutorialsCompleted || 0) >= 1;
        break;
      case 'testing_guru':
        shouldUnlock = (progressData.testsRun || 0) >= 5;
        break;
      case 'collaboration_champion':
        shouldUnlock = (progressData.collaborationSessions || 0) >= 1;
        break;
    }

    if (shouldUnlock) {
      const newAchievement = {
        id,
        earnedAt: new Date().toISOString()
      };
      newAchievements.push(newAchievement);
      currentAchievements.push(newAchievement);
    }
  });

  // Save new achievements and show notifications
  if (newAchievements.length > 0) {
    localStorage.setItem('app-builder-achievements', JSON.stringify(currentAchievements));

    newAchievements.forEach((achievement, index) => {
      setTimeout(() => {
        showAchievementNotification(achievement);
      }, index * 1000); // Stagger notifications
    });
  }
};
