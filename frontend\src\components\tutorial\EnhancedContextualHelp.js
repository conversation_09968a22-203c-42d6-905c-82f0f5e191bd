/**
 * Enhanced Contextual Help Component
 * 
 * Provides smart, context-aware help overlays that appear based on user actions
 * and workflow stage. Integrates with the tutorial system for seamless guidance.
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Card, Button, Space, Typography, Tag, Tooltip } from 'antd';
import {
  BulbOutlined,
  PlayCircleOutlined,
  CloseOutlined,
  QuestionCircleOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text, Paragraph } = Typography;

// Styled Components
const HelpOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1001;
`;

const HelpCard = styled(Card)`
  position: absolute;
  max-width: 320px;
  pointer-events: auto;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  border: 2px solid #1890ff;
  background: white;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    padding: 12px 16px;
    min-height: auto;
  }
  
  .ant-card-head-title {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    z-index: 1002;
  }
  
  /* Arrow positioning based on placement */
  &.placement-top::before {
    bottom: -8px;
    left: 50%;
    margin-left: -8px;
    border-width: 8px 8px 0 8px;
    border-color: #1890ff transparent transparent transparent;
  }
  
  &.placement-bottom::before {
    top: -8px;
    left: 50%;
    margin-left: -8px;
    border-width: 0 8px 8px 8px;
    border-color: transparent transparent #1890ff transparent;
  }
  
  &.placement-left::before {
    right: -8px;
    top: 50%;
    margin-top: -8px;
    border-width: 8px 0 8px 8px;
    border-color: transparent transparent transparent #1890ff;
  }
  
  &.placement-right::before {
    left: -8px;
    top: 50%;
    margin-top: -8px;
    border-width: 8px 8px 8px 0;
    border-color: transparent #1890ff transparent transparent;
  }
`;

const PulseIndicator = styled.div`
  position: absolute;
  width: 12px;
  height: 12px;
  background: #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.7;
    }
    100% {
      transform: scale(0.8);
      opacity: 1;
    }
  }
`;

/**
 * Enhanced Contextual Help Component
 */
const EnhancedContextualHelp = ({ 
  onStartTutorial,
  isEnabled = true,
  showAnimations = true 
}) => {
  const [activeHelp, setActiveHelp] = useState(null);
  const [helpPosition, setHelpPosition] = useState({ x: 0, y: 0 });
  const [helpPlacement, setHelpPlacement] = useState('bottom');
  const [pulseIndicators, setPulseIndicators] = useState([]);
  
  const helpTimeoutRef = useRef(null);
  const pulseTimeoutRef = useRef(null);

  // Calculate optimal position for help card
  const calculatePosition = useCallback((targetElement, cardWidth = 320, cardHeight = 200) => {
    if (!targetElement) return { x: 0, y: 0, placement: 'bottom' };

    const rect = targetElement.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let x, y, placement;

    // Try bottom first (preferred)
    if (rect.bottom + cardHeight + 20 <= viewport.height) {
      placement = 'bottom';
      x = Math.max(10, Math.min(rect.left + rect.width / 2 - cardWidth / 2, viewport.width - cardWidth - 10));
      y = rect.bottom + 12;
    }
    // Try top
    else if (rect.top - cardHeight - 20 >= 0) {
      placement = 'top';
      x = Math.max(10, Math.min(rect.left + rect.width / 2 - cardWidth / 2, viewport.width - cardWidth - 10));
      y = rect.top - cardHeight - 12;
    }
    // Try right
    else if (rect.right + cardWidth + 20 <= viewport.width) {
      placement = 'right';
      x = rect.right + 12;
      y = Math.max(10, Math.min(rect.top + rect.height / 2 - cardHeight / 2, viewport.height - cardHeight - 10));
    }
    // Try left
    else if (rect.left - cardWidth - 20 >= 0) {
      placement = 'left';
      x = rect.left - cardWidth - 12;
      y = Math.max(10, Math.min(rect.top + rect.height / 2 - cardHeight / 2, viewport.height - cardHeight - 10));
    }
    // Fallback to center
    else {
      placement = 'center';
      x = viewport.width / 2 - cardWidth / 2;
      y = viewport.height / 2 - cardHeight / 2;
    }

    return { x, y, placement };
  }, []);

  // Show contextual help
  const showHelp = useCallback((helpData) => {
    if (!isEnabled || !helpData) return;

    const { text, element, priority, tutorialSuggestion, title, actions } = helpData;
    
    // Clear existing timeout
    if (helpTimeoutRef.current) {
      clearTimeout(helpTimeoutRef.current);
    }

    // Calculate position
    const position = calculatePosition(element);
    setHelpPosition(position);
    setHelpPlacement(position.placement);

    // Set help content
    setActiveHelp({
      title: title || 'Helpful Tip',
      text,
      priority,
      tutorialSuggestion,
      actions: actions || [],
      element
    });

    // Auto-hide based on priority
    const autoHideDelay = priority === 'high' ? 8000 : priority === 'medium' ? 6000 : 4000;
    helpTimeoutRef.current = setTimeout(() => {
      setActiveHelp(null);
    }, autoHideDelay);

  }, [isEnabled, calculatePosition]);

  // Hide help
  const hideHelp = useCallback(() => {
    setActiveHelp(null);
    if (helpTimeoutRef.current) {
      clearTimeout(helpTimeoutRef.current);
    }
  }, []);

  // Show pulse indicators for important elements
  const showPulseIndicators = useCallback((elements) => {
    if (!showAnimations) return;

    const indicators = elements.map(element => {
      const rect = element.getBoundingClientRect();
      return {
        id: Math.random().toString(36).substr(2, 9),
        x: rect.right - 6,
        y: rect.top - 6,
        element
      };
    });

    setPulseIndicators(indicators);

    // Auto-hide pulse indicators
    pulseTimeoutRef.current = setTimeout(() => {
      setPulseIndicators([]);
    }, 10000);
  }, [showAnimations]);

  // Handle tutorial start
  const handleStartTutorial = useCallback((tutorialId) => {
    hideHelp();
    if (onStartTutorial) {
      onStartTutorial(tutorialId);
    }
  }, [hideHelp, onStartTutorial]);

  // Listen for contextual help events
  useEffect(() => {
    const handleContextualHelpEvent = (event) => {
      showHelp(event.detail);
    };

    const handlePulseIndicatorEvent = (event) => {
      showPulseIndicators(event.detail.elements);
    };

    window.addEventListener('showContextualHelp', handleContextualHelpEvent);
    window.addEventListener('showPulseIndicators', handlePulseIndicatorEvent);

    return () => {
      window.removeEventListener('showContextualHelp', handleContextualHelpEvent);
      window.removeEventListener('showPulseIndicators', handlePulseIndicatorEvent);
      if (helpTimeoutRef.current) clearTimeout(helpTimeoutRef.current);
      if (pulseTimeoutRef.current) clearTimeout(pulseTimeoutRef.current);
    };
  }, [showHelp, showPulseIndicators]);

  // Update position on window resize
  useEffect(() => {
    if (!activeHelp?.element) return;

    const handleResize = () => {
      const position = calculatePosition(activeHelp.element);
      setHelpPosition(position);
      setHelpPlacement(position.placement);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activeHelp, calculatePosition]);

  // Render help card
  const renderHelpCard = () => {
    if (!activeHelp) return null;

    return (
      <HelpCard
        className={`placement-${helpPlacement}`}
        style={{
          left: helpPosition.x,
          top: helpPosition.y,
          zIndex: 1002
        }}
        title={
          <Space>
            <BulbOutlined />
            {activeHelp.title}
            {activeHelp.priority && (
              <Tag 
                color={activeHelp.priority === 'high' ? 'red' : activeHelp.priority === 'medium' ? 'orange' : 'blue'}
                size="small"
              >
                {activeHelp.priority}
              </Tag>
            )}
          </Space>
        }
        extra={
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={hideHelp}
            style={{ color: 'white' }}
            size="small"
          />
        }
        size="small"
      >
        <Paragraph style={{ margin: 0, marginBottom: 12, fontSize: 13 }}>
          {activeHelp.text}
        </Paragraph>
        
        <Space size="small">
          {activeHelp.tutorialSuggestion && (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartTutorial(activeHelp.tutorialSuggestion)}
            >
              Start Tutorial
            </Button>
          )}
          
          {activeHelp.actions.map((action, index) => (
            <Button
              key={index}
              size="small"
              onClick={action.onClick}
              type={action.type || 'default'}
              icon={action.icon}
            >
              {action.label}
            </Button>
          ))}
          
          <Button
            size="small"
            onClick={hideHelp}
            icon={<ArrowRightOutlined />}
          >
            Got it
          </Button>
        </Space>
      </HelpCard>
    );
  };

  // Render pulse indicators
  const renderPulseIndicators = () => {
    return pulseIndicators.map(indicator => (
      <PulseIndicator
        key={indicator.id}
        style={{
          left: indicator.x,
          top: indicator.y,
          zIndex: 1001
        }}
      />
    ));
  };

  if (!isEnabled) return null;

  return createPortal(
    <HelpOverlay>
      {renderHelpCard()}
      {renderPulseIndicators()}
    </HelpOverlay>,
    document.body
  );
};

export default EnhancedContextualHelp;

// Utility function to trigger contextual help from other components
export const triggerContextualHelp = (helpData) => {
  const event = new CustomEvent('showContextualHelp', {
    detail: helpData
  });
  window.dispatchEvent(event);
};

// Utility function to show pulse indicators
export const showPulseIndicators = (elements) => {
  const event = new CustomEvent('showPulseIndicators', {
    detail: { elements }
  });
  window.dispatchEvent(event);
};
