/**
 * Simple App Builder - Optimized for Bundle Size
 * 
 * A lightweight app builder with core functionality and minimal dependencies.
 * Advanced features are loaded dynamically only when needed.
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Layout, Button, Typography, Card, Space, Divider } from '../../utils/optimizedAntdImports';
import { 
  AppstoreOutlined, 
  LayoutOutlined, 
  BgColorsOutlined, 
  PlayCircleOutlined,
  SaveOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

// Simple component types
const COMPONENT_TYPES = [
  { type: 'button', label: 'Button', icon: '🔘' },
  { type: 'text', label: 'Text', icon: '📝' },
  { type: 'input', label: 'Input', icon: '📝' },
  { type: 'card', label: 'Card', icon: '🃏' },
  { type: 'image', label: 'Image', icon: '🖼️' },
  { type: 'divider', label: 'Divider', icon: '➖' }
];

// Simple component renderer
const ComponentRenderer = ({ component, isSelected, onSelect, onUpdate }) => {
  const style = {
    position: 'absolute',
    left: component.x || 0,
    top: component.y || 0,
    padding: '8px',
    border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
    borderRadius: '4px',
    background: '#fff',
    cursor: 'pointer',
    minWidth: '80px',
    minHeight: '32px'
  };

  const renderContent = () => {
    switch (component.type) {
      case 'button':
        return <Button size="small">{component.props?.text || 'Button'}</Button>;
      case 'text':
        return <Text>{component.props?.text || 'Text'}</Text>;
      case 'input':
        return <input placeholder={component.props?.placeholder || 'Input'} style={{ width: '100%' }} />;
      case 'card':
        return (
          <Card size="small" style={{ width: 150 }}>
            {component.props?.title || 'Card Title'}
          </Card>
        );
      case 'image':
        return (
          <div style={{ width: 100, height: 60, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            🖼️
          </div>
        );
      case 'divider':
        return <Divider style={{ margin: '8px 0' }} />;
      default:
        return <div>{component.type}</div>;
    }
  };

  return (
    <div style={style} onClick={() => onSelect(component)}>
      {renderContent()}
    </div>
  );
};

// Component palette
const ComponentPalette = ({ onAddComponent }) => (
  <div style={{ padding: '16px' }}>
    <Title level={5}>Components</Title>
    <Space direction="vertical" style={{ width: '100%' }}>
      {COMPONENT_TYPES.map(({ type, label, icon }) => (
        <Button
          key={type}
          block
          size="small"
          onClick={() => onAddComponent(type)}
          style={{ textAlign: 'left' }}
        >
          {icon} {label}
        </Button>
      ))}
    </Space>
  </div>
);

// Property editor
const PropertyEditor = ({ component, onUpdate }) => {
  if (!component) {
    return (
      <div style={{ padding: '16px' }}>
        <Title level={5}>Properties</Title>
        <Text type="secondary">Select a component to edit properties</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '16px' }}>
      <Title level={5}>Properties</Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Type:</Text> {component.type}
        </div>
        <div>
          <Text strong>ID:</Text> {component.id}
        </div>
        {component.type === 'button' || component.type === 'text' ? (
          <div>
            <Text strong>Text:</Text>
            <input
              value={component.props?.text || ''}
              onChange={(e) => onUpdate(component.id, { 
                ...component.props, 
                text: e.target.value 
              })}
              style={{ width: '100%', marginTop: '4px' }}
            />
          </div>
        ) : null}
      </Space>
    </div>
  );
};

// Main Simple App Builder Component
export default function SimpleAppBuilder({ 
  projectId = 'simple-project',
  initialComponents = [],
  enableFeatures = {},
  onError
}) {
  const [components, setComponents] = useState(initialComponents);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);

  // Add component
  const handleAddComponent = useCallback((type) => {
    const newComponent = {
      id: `${type}_${Date.now()}`,
      type,
      props: { 
        text: type === 'button' ? 'Button' : type === 'text' ? 'Text' : undefined,
        placeholder: type === 'input' ? 'Enter text...' : undefined,
        title: type === 'card' ? 'Card Title' : undefined
      },
      x: Math.random() * 300,
      y: Math.random() * 200
    };
    setComponents(prev => [...prev, newComponent]);
  }, []);

  // Update component
  const handleUpdateComponent = useCallback((id, newProps) => {
    setComponents(prev => prev.map(comp => 
      comp.id === id ? { ...comp, props: newProps } : comp
    ));
  }, []);

  // Delete component
  const handleDeleteComponent = useCallback((id) => {
    setComponents(prev => prev.filter(comp => comp.id !== id));
    if (selectedComponent?.id === id) {
      setSelectedComponent(null);
    }
  }, [selectedComponent]);

  // Header content
  const headerContent = useMemo(() => (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
      <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
        App Builder 201 - Simple
      </Title>
      <Space>
        <Button 
          type={previewMode ? 'default' : 'primary'} 
          icon={<PlayCircleOutlined />}
          onClick={() => setPreviewMode(!previewMode)}
        >
          {previewMode ? 'Edit' : 'Preview'}
        </Button>
        <Button icon={<SaveOutlined />}>Save</Button>
        <Button icon={<SettingOutlined />}>Settings</Button>
      </Space>
    </div>
  ), [previewMode]);

  // Canvas content
  const canvasContent = useMemo(() => {
    if (components.length === 0) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          color: '#999'
        }}>
          <Title level={4} type="secondary">Welcome to Simple App Builder</Title>
          <Text type="secondary">Add components from the left panel to get started</Text>
        </div>
      );
    }

    return components.map(component => (
      <ComponentRenderer
        key={component.id}
        component={component}
        isSelected={selectedComponent?.id === component.id}
        onSelect={setSelectedComponent}
        onUpdate={handleUpdateComponent}
      />
    ));
  }, [components, selectedComponent, handleUpdateComponent]);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        {headerContent}
      </Header>
      
      <Layout>
        {!previewMode && (
          <Sider width={250} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
            <ComponentPalette onAddComponent={handleAddComponent} />
          </Sider>
        )}
        
        <Content style={{ padding: '24px', background: '#f5f5f5' }}>
          <Card
            title="Canvas"
            style={{ height: '100%', minHeight: '500px' }}
            bodyStyle={{ position: 'relative', height: '100%', minHeight: '450px' }}
          >
            {canvasContent}
          </Card>
        </Content>
        
        {!previewMode && (
          <Sider width={250} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
            <PropertyEditor 
              component={selectedComponent} 
              onUpdate={handleUpdateComponent}
            />
            {selectedComponent && (
              <div style={{ padding: '16px' }}>
                <Button 
                  danger 
                  block 
                  onClick={() => handleDeleteComponent(selectedComponent.id)}
                >
                  Delete Component
                </Button>
              </div>
            )}
          </Sider>
        )}
      </Layout>
    </Layout>
  );
}
