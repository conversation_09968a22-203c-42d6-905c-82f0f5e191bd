/* Minimal Ant Design CSS for Bundle Size Optimization */
/* Only includes essential styles for core components */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
}

/* Essential button styles */
.ant-btn {
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 6px;
  outline: 0;
}

.ant-btn-primary {
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* Essential card styles */
.ant-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  position: relative;
  display: inline-block;
  width: 100%;
}

.ant-card-head {
  background: transparent;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
  border-radius: 8px 8px 0 0;
  zoom: 1;
  margin-bottom: -1px;
  min-height: 56px;
}

.ant-card-body {
  padding: 24px;
  zoom: 1;
}

/* Essential layout styles */
.ant-layout {
  display: flex;
  flex: auto;
  flex-direction: column;
  min-height: 0;
  background: #f0f2f5;
}

.ant-layout-header {
  background: #001529;
  padding: 0 50px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-content {
  flex: auto;
  min-height: 0;
}

/* Essential typography styles */
.ant-typography {
  color: rgba(0, 0, 0, 0.85);
  overflow-wrap: break-word;
}

.ant-typography h1 {
  font-size: 38px;
  line-height: 1.23;
  font-weight: 600;
  margin-bottom: 0.5em;
}

.ant-typography h2 {
  font-size: 30px;
  line-height: 1.35;
  font-weight: 600;
  margin-bottom: 0.5em;
}

.ant-typography h3 {
  font-size: 24px;
  line-height: 1.35;
  font-weight: 600;
  margin-bottom: 0.5em;
}

/* Essential spin styles */
.ant-spin {
  color: #1890ff;
  text-align: center;
  vertical-align: middle;
  opacity: 1;
  transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

.ant-spin-spinning {
  position: static;
  display: inline-block;
}

.ant-spin-dot {
  position: relative;
  display: inline-block;
  font-size: 20px;
  width: 1em;
  height: 1em;
}

.ant-spin-dot-item {
  position: absolute;
  display: block;
  width: 9px;
  height: 9px;
  background-color: #1890ff;
  border-radius: 100%;
  transform: scale(0.75);
  transform-origin: 50% 50%;
  opacity: 0.3;
  animation: antSpinMove 1s infinite linear alternate;
}

@keyframes antSpinMove {
  to {
    opacity: 1;
  }
}

/* Essential alert styles */
.ant-alert {
  position: relative;
  padding: 8px 15px;
  margin-bottom: 16px;
  border: 1px solid transparent;
  border-radius: 6px;
}

.ant-alert-warning {
  background-color: #fffbe6;
  border-color: #ffe58f;
}

.ant-alert-error {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.ant-alert-info {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.ant-alert-success {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

/* Essential space styles */
.ant-space {
  display: inline-flex;
  align-items: center;
}

.ant-space-vertical {
  flex-direction: column;
}

.ant-space-item {
  display: flex;
}

/* Utility classes */
.ant-row {
  display: flex;
  flex-flow: row wrap;
}

.ant-col {
  position: relative;
  max-width: 100%;
  min-height: 1px;
}

/* Responsive utilities */
@media (max-width: 575px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
}

/* Dark mode support (minimal) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #141414;
    color: rgba(255, 255, 255, 0.85);
  }
  
  .ant-card {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .ant-layout {
    background: #000;
  }
}
