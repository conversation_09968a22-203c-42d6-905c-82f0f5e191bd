/**
 * Workflow Tutorial Guide Component
 * 
 * Provides step-by-step guidance for complete workflows like creating
 * an app from template selection to code export with interactive validation.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Steps, 
  Button, 
  Space, 
  Typography, 
  Progress, 
  Tag, 
  Alert,
  Divider,
  Timeline,
  Tooltip
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RocketOutlined,
  BulbOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled Components
const GuideContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  width: 380px;
  max-height: calc(100vh - 40px);
  z-index: 999;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

const GuideCard = styled(Card)`
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  border: none;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
  }
  
  .ant-card-head-title {
    color: white;
    font-weight: 600;
  }
  
  .ant-card-body {
    padding: 20px;
  }
`;

const StepCard = styled(Card)`
  margin-bottom: 12px;
  border: 1px solid ${props => 
    props.status === 'completed' ? '#52c41a' : 
    props.status === 'active' ? '#1890ff' : 
    props.status === 'error' ? '#ff4d4f' : '#d9d9d9'
  };
  border-radius: 8px;
  
  .ant-card-body {
    padding: 12px 16px;
  }
  
  ${props => props.status === 'active' && `
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    animation: glow 2s ease-in-out infinite alternate;
    
    @keyframes glow {
      from { box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); }
      to { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1); }
    }
  `}
`;

// Workflow definitions
const COMPLETE_WORKFLOW_STEPS = [
  {
    id: 'template-selection',
    title: 'Choose Template',
    description: 'Select a template that matches your app idea',
    target: '[data-tutorial="template-selector"]',
    validation: () => document.querySelector('[data-template-selected="true"]'),
    estimatedTime: 2,
    tips: ['Browse different categories', 'Preview templates before selecting', 'Consider your target audience']
  },
  {
    id: 'add-components',
    title: 'Add Components',
    description: 'Drag components from the palette to build your interface',
    target: '[data-tutorial="component-palette"]',
    validation: () => document.querySelectorAll('[data-tutorial="canvas-area"] .component').length > 0,
    estimatedTime: 5,
    tips: ['Start with basic components', 'Use containers to organize', 'Think about user flow']
  },
  {
    id: 'customize-properties',
    title: 'Customize Properties',
    description: 'Configure component properties to match your design',
    target: '[data-tutorial="property-editor"]',
    validation: () => document.querySelector('[data-properties-modified="true"]'),
    estimatedTime: 3,
    tips: ['Adjust colors and fonts', 'Set appropriate sizes', 'Configure interactions']
  },
  {
    id: 'design-layout',
    title: 'Design Layout',
    description: 'Arrange components in responsive layouts',
    target: '[data-tutorial="layout-designer"]',
    validation: () => document.querySelector('[data-layout-applied="true"]'),
    estimatedTime: 4,
    tips: ['Consider mobile responsiveness', 'Use grid systems', 'Test different screen sizes']
  },
  {
    id: 'apply-theme',
    title: 'Apply Theme',
    description: 'Customize the visual appearance with themes',
    target: '[data-tutorial="theme-manager"]',
    validation: () => document.querySelector('[data-theme-applied="true"]'),
    estimatedTime: 3,
    tips: ['Choose consistent colors', 'Select readable fonts', 'Consider accessibility']
  },
  {
    id: 'test-app',
    title: 'Test Application',
    description: 'Validate functionality and user experience',
    target: '[data-tutorial="testing-tools"]',
    validation: () => document.querySelector('[data-tests-run="true"]'),
    estimatedTime: 4,
    tips: ['Test all interactions', 'Check responsiveness', 'Validate accessibility']
  },
  {
    id: 'preview-app',
    title: 'Preview Application',
    description: 'See how your app looks to end users',
    target: '[data-tutorial="preview-mode"]',
    validation: () => document.querySelector('[data-preview-active="true"]'),
    estimatedTime: 2,
    tips: ['Test user interactions', 'Check visual consistency', 'Verify responsive behavior']
  },
  {
    id: 'export-code',
    title: 'Export Code',
    description: 'Generate production-ready code for your chosen framework',
    target: '[data-tutorial="code-export"]',
    validation: () => document.querySelector('[data-code-exported="true"]'),
    estimatedTime: 3,
    tips: ['Choose your framework', 'Configure export settings', 'Review generated code']
  }
];

/**
 * Workflow Tutorial Guide Component
 */
const WorkflowTutorialGuide = ({
  isVisible = false,
  onClose,
  onStepClick,
  onStartWorkflow,
  currentWorkflowStep = 0,
  workflowProgress = {}
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [stepStatuses, setStepStatuses] = useState({});
  const [isValidating, setIsValidating] = useState(false);

  // Calculate overall progress
  const overallProgress = Math.round((completedSteps.size / COMPLETE_WORKFLOW_STEPS.length) * 100);

  // Validate current step
  const validateCurrentStep = useCallback(async () => {
    const currentStep = COMPLETE_WORKFLOW_STEPS[activeStep];
    if (!currentStep?.validation) return true;

    setIsValidating(true);
    
    // Simulate validation delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const isValid = currentStep.validation();
    
    setStepStatuses(prev => ({
      ...prev,
      [currentStep.id]: isValid ? 'completed' : 'error'
    }));

    if (isValid) {
      setCompletedSteps(prev => new Set([...prev, currentStep.id]));
      
      // Auto-advance to next step
      setTimeout(() => {
        if (activeStep < COMPLETE_WORKFLOW_STEPS.length - 1) {
          setActiveStep(activeStep + 1);
        }
      }, 1000);
    }
    
    setIsValidating(false);
    return isValid;
  }, [activeStep]);

  // Handle step click
  const handleStepClick = useCallback((stepIndex) => {
    setActiveStep(stepIndex);
    if (onStepClick) {
      onStepClick(COMPLETE_WORKFLOW_STEPS[stepIndex]);
    }
  }, [onStepClick]);

  // Get step status
  const getStepStatus = useCallback((stepId, stepIndex) => {
    if (completedSteps.has(stepId)) return 'completed';
    if (stepIndex === activeStep) return 'active';
    if (stepStatuses[stepId] === 'error') return 'error';
    return 'pending';
  }, [completedSteps, activeStep, stepStatuses]);

  // Get step icon
  const getStepIcon = useCallback((status) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'active': return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'error': return <WarningOutlined style={{ color: '#ff4d4f' }} />;
      default: return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  }, []);

  // Render step card
  const renderStepCard = useCallback((step, index) => {
    const status = getStepStatus(step.id, index);
    const isActive = index === activeStep;
    
    return (
      <StepCard
        key={step.id}
        status={status}
        size="small"
        onClick={() => handleStepClick(index)}
        style={{ cursor: 'pointer' }}
      >
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
          <div style={{ marginTop: 2 }}>
            {getStepIcon(status)}
          </div>
          
          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
              <Text strong style={{ fontSize: 14 }}>
                {step.title}
              </Text>
              <Tag size="small" color="blue">
                {step.estimatedTime}m
              </Tag>
            </div>
            
            <Paragraph style={{ margin: 0, fontSize: 12, color: '#666' }}>
              {step.description}
            </Paragraph>
            
            {isActive && step.tips && (
              <div style={{ marginTop: 8, padding: 8, background: '#f0f9ff', borderRadius: 4 }}>
                <Text style={{ fontSize: 11, color: '#1890ff' }}>
                  <BulbOutlined /> Tips: {step.tips.join(' • ')}
                </Text>
              </div>
            )}
            
            {status === 'error' && (
              <Alert
                message="Validation failed"
                description="Complete this step to continue"
                type="error"
                size="small"
                style={{ marginTop: 8 }}
                showIcon
              />
            )}
          </div>
        </div>
      </StepCard>
    );
  }, [activeStep, getStepStatus, getStepIcon, handleStepClick]);

  if (!isVisible) return null;

  return (
    <GuideContainer>
      <GuideCard
        title={
          <Space>
            <RocketOutlined />
            Complete Workflow Guide
            <Tag color="blue">{completedSteps.size}/{COMPLETE_WORKFLOW_STEPS.length}</Tag>
          </Space>
        }
        extra={
          <Button
            type="text"
            onClick={onClose}
            style={{ color: 'white' }}
            size="small"
          >
            ×
          </Button>
        }
      >
        {/* Overall Progress */}
        <div style={{ marginBottom: 20 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text strong>Overall Progress</Text>
            <Text>{overallProgress}%</Text>
          </div>
          <Progress 
            percent={overallProgress} 
            size="small"
            strokeColor={{
              '0%': '#1890ff',
              '100%': '#52c41a',
            }}
          />
        </div>

        {/* Current Step Info */}
        {activeStep < COMPLETE_WORKFLOW_STEPS.length && (
          <Alert
            message={`Current: ${COMPLETE_WORKFLOW_STEPS[activeStep].title}`}
            description={COMPLETE_WORKFLOW_STEPS[activeStep].description}
            type="info"
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 16 }}
            showIcon
          />
        )}

        {/* Steps List */}
        <div style={{ marginBottom: 16 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
            Workflow Steps
          </Title>
          {COMPLETE_WORKFLOW_STEPS.map((step, index) => renderStepCard(step, index))}
        </div>

        {/* Actions */}
        <Divider />
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={onStartWorkflow}
            disabled={completedSteps.size === COMPLETE_WORKFLOW_STEPS.length}
          >
            {completedSteps.size === 0 ? 'Start Workflow' : 'Continue'}
          </Button>
          
          <Button
            onClick={validateCurrentStep}
            loading={isValidating}
            disabled={activeStep >= COMPLETE_WORKFLOW_STEPS.length}
          >
            Validate Step
          </Button>
        </Space>

        {/* Completion Message */}
        {completedSteps.size === COMPLETE_WORKFLOW_STEPS.length && (
          <Alert
            message="Workflow Complete! 🎉"
            description="You've successfully completed the entire app creation workflow!"
            type="success"
            style={{ marginTop: 16 }}
            showIcon
          />
        )}
      </GuideCard>
    </GuideContainer>
  );
};

export default WorkflowTutorialGuide;
