import React, { useState, useEffect } from 'react';
import Card from 'antd/es/card';
import Typography from 'antd/es/typography';
import Input from 'antd/es/input';
import Button from 'antd/es/button';
import List from 'antd/es/list';
import Badge from 'antd/es/badge';
import Space from 'antd/es/space';
import WifiOutlined from '@ant-design/icons/es/icons/WifiOutlined';
import SendOutlined from '@ant-design/icons/es/icons/SendOutlined';
import ClearOutlined from '@ant-design/icons/es/icons/ClearOutlined';

const { Title, Text } = Typography;

/**
 * Simple WebSocket Page - Optimized for Bundle Size
 * Basic WebSocket testing functionality without heavy dependencies
 */
const SimpleWebSocketPage = () => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [url, setUrl] = useState('ws://localhost:8000/ws/test/');

  // Connect to WebSocket
  const connect = () => {
    try {
      const ws = new WebSocket(url);

      ws.onopen = () => {
        setConnected(true);
        addMessage('Connected to WebSocket', 'system');
      };

      ws.onmessage = (event) => {
        addMessage(event.data, 'received');
      };

      ws.onclose = () => {
        setConnected(false);
        addMessage('Disconnected from WebSocket', 'system');
      };

      ws.onerror = (error) => {
        addMessage(`Error: ${error.message || 'Connection failed'}`, 'error');
      };

      setSocket(ws);
    } catch (error) {
      addMessage(`Connection error: ${error.message}`, 'error');
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (socket) {
      socket.close();
      setSocket(null);
    }
  };

  // Send message
  const sendMessage = () => {
    if (socket && connected && inputMessage.trim()) {
      socket.send(inputMessage);
      addMessage(inputMessage, 'sent');
      setInputMessage('');
    }
  };

  // Add message to list
  const addMessage = (content, type) => {
    const message = {
      id: Date.now(),
      content,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(prev => [...prev, message]);
  };

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };

  // Handle Enter key
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, [socket]);

  const getMessageStyle = (type) => {
    const baseStyle = {
      padding: '8px',
      marginBottom: '8px',
      borderRadius: '4px'
    };

    switch (type) {
      case 'sent':
        return { ...baseStyle, backgroundColor: '#e6f7ff', borderLeft: '3px solid #1890ff' };
      case 'received':
        return { ...baseStyle, backgroundColor: '#f6ffed', borderLeft: '3px solid #52c41a' };
      case 'error':
        return { ...baseStyle, backgroundColor: '#fff2f0', borderLeft: '3px solid #ff4d4f' };
      case 'system':
        return { ...baseStyle, backgroundColor: '#fafafa', borderLeft: '3px solid #d9d9d9' };
      default:
        return baseStyle;
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>
        <WifiOutlined /> WebSocket Test
      </Title>

      {/* Connection Controls */}
      <Card title="Connection" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>WebSocket URL:</Text>
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="ws://localhost:8000/ws/test/"
              style={{ marginTop: '4px' }}
              disabled={connected}
            />
          </div>

          <Space>
            <Button
              type="primary"
              onClick={connect}
              disabled={connected}
              loading={false}
            >
              Connect
            </Button>
            <Button
              onClick={disconnect}
              disabled={!connected}
            >
              Disconnect
            </Button>
            <Badge
              status={connected ? 'success' : 'error'}
              text={connected ? 'Connected' : 'Disconnected'}
            />
          </Space>
        </Space>
      </Card>

      {/* Message Controls */}
      <Card title="Send Message" style={{ marginBottom: '16px' }}>
        <Space.Compact style={{ width: '100%' }}>
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            disabled={!connected}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={sendMessage}
            disabled={!connected || !inputMessage.trim()}
          >
            Send
          </Button>
        </Space.Compact>
      </Card>

      {/* Messages */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>Messages ({messages.length})</span>
            <Button
              size="small"
              icon={<ClearOutlined />}
              onClick={clearMessages}
              disabled={messages.length === 0}
            >
              Clear
            </Button>
          </div>
        }
      >
        <div style={{
          height: '300px',
          overflowY: 'auto',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          padding: '8px'
        }}>
          {messages.length === 0 ? (
            <div style={{
              textAlign: 'center',
              color: '#999',
              padding: '40px',
              fontStyle: 'italic'
            }}>
              No messages yet. Connect and send a message to get started.
            </div>
          ) : (
            <List
              dataSource={messages}
              renderItem={(message) => (
                <div key={message.id} style={getMessageStyle(message.type)}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '4px',
                    fontSize: '12px',
                    color: '#666'
                  }}>
                    <span style={{ fontWeight: 'bold', textTransform: 'uppercase' }}>
                      {message.type}
                    </span>
                    <span>{message.timestamp}</span>
                  </div>
                  <div style={{
                    fontFamily: 'monospace',
                    fontSize: '14px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word'
                  }}>
                    {message.content}
                  </div>
                </div>
              )}
            />
          )}
        </div>
      </Card>

      {/* Help */}
      <Card title="Help" size="small" style={{ marginTop: '16px' }}>
        <Text type="secondary">
          This is a simple WebSocket testing tool. Enter a WebSocket URL, connect, and start sending messages.
          The default URL connects to the local development server.
        </Text>
      </Card>
    </div>
  );
};

export default SimpleWebSocketPage;
