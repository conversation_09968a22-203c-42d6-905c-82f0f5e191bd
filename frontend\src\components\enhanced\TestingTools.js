import React, { useState } from 'react';
import { Card, Button, Input, Switch, Typography, Space, Divider, Alert, Collapse, Spin, Statistic, Row, Col, Progress, Tooltip } from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  ApiOutlined,
  DashboardOutlined,
  WarningOutlined,
  LoadingOutlined,
  LinkOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { runAllTests, testWebSocketConnection, testApiConnection, testBrowserPerformance } from '../../utils/testUtils';
import { getWebSocketUrl, getApiUrl } from '../../config/env';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const TestingContainer = styled.div`
  padding: 20px;
`;

const TestCard = styled(Card)`
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;

  .label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const InfoBox = styled(Alert)`
  margin-bottom: 16px;
`;

const ResultCard = styled(Card)`
  margin-top: 16px;

  .ant-card-head {
    background-color: ${props => {
    if (props.status === 'success') return 'rgba(82, 196, 26, 0.1)';
    if (props.status === 'error') return 'rgba(245, 34, 45, 0.1)';
    if (props.status === 'warning') return 'rgba(250, 173, 20, 0.1)';
    return 'transparent';
  }};
  }
`;

const MetricCard = styled(Card)`
  height: 100%;

  .ant-statistic-title {
    font-size: 14px;
  }

  .ant-statistic-content {
    font-size: 24px;
  }
`;

const StatusIcon = ({ status }) => {
  if (status === 'success') return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
  if (status === 'error') return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
  if (status === 'warning') return <WarningOutlined style={{ color: '#faad14' }} />;
  if (status === 'loading') return <LoadingOutlined style={{ color: '#1890ff' }} />;
  return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
};

/**
 * TestingTools component
 * Provides tools for testing the application
 */
const TestingTools = ({
  components = [],
  onTestComplete,
  onTestStart,
  enabledTests = ['component', 'layout', 'accessibility', 'performance'],
  autoRun = false,
  showMetrics = true
}) => {
  // Test configuration
  const [websocketUrl, setWebsocketUrl] = useState(getWebSocketUrl('app_builder'));
  const [apiUrl, setApiUrl] = useState(getApiUrl('health'));
  const [testPerformance, setTestPerformance] = useState(true);

  // Test state
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [componentTestResults, setComponentTestResults] = useState(null);

  // Component testing function
  const runComponentTests = async () => {
    if (onTestStart) onTestStart('component');

    const testResults = [];

    // Test each component
    for (const component of components) {
      try {
        // Basic component validation
        const componentTest = {
          id: component.id,
          type: component.type,
          status: 'passed',
          message: 'Component structure is valid',
          timestamp: new Date().toISOString()
        };

        // Check required properties
        if (!component.type) {
          componentTest.status = 'failed';
          componentTest.message = 'Component missing type property';
        } else if (!component.id) {
          componentTest.status = 'failed';
          componentTest.message = 'Component missing id property';
        }

        testResults.push(componentTest);
      } catch (error) {
        testResults.push({
          id: component.id || 'unknown',
          type: component.type || 'unknown',
          status: 'failed',
          message: `Component test failed: ${error.message}`,
          timestamp: new Date().toISOString()
        });
      }
    }

    setComponentTestResults(testResults);
    if (onTestComplete) onTestComplete('component', testResults);
    return testResults;
  };

  // Handle running all tests
  const handleRunAllTests = async () => {
    setLoading(true);
    setResults(null);
    setComponentTestResults(null);

    try {
      // Run component tests if enabled
      let componentResults = null;
      if (enabledTests.includes('component') && components.length > 0) {
        componentResults = await runComponentTests();
      }

      // Run system tests
      const testResults = await runAllTests({
        websocketUrl,
        apiUrl,
        testPerformance
      });

      // Combine results
      const allResults = {
        ...testResults,
        components: componentResults
      };

      setResults(allResults);
      if (onTestComplete) onTestComplete('all', allResults);
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle running WebSocket test
  const handleTestWebSocket = async () => {
    setLoading(true);

    try {
      const wsResults = await testWebSocketConnection(websocketUrl);

      setResults(prev => ({
        ...prev,
        websocket: wsResults
      }));
    } catch (error) {
      console.error('WebSocket test error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle running API test
  const handleTestApi = async () => {
    setLoading(true);

    try {
      const apiResults = await testApiConnection(apiUrl);

      setResults(prev => ({
        ...prev,
        api: apiResults
      }));
    } catch (error) {
      console.error('API test error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle running performance test
  const handleTestPerformance = () => {
    setLoading(true);

    try {
      const perfResults = testBrowserPerformance();

      setResults(prev => ({
        ...prev,
        performance: perfResults
      }));
    } catch (error) {
      console.error('Performance test error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render WebSocket test results
  const renderWebSocketResults = () => {
    if (!results || !results.websocket) return null;

    const { websocket } = results;
    const status = websocket.success ? 'success' : 'error';

    return (
      <ResultCard
        title={
          <Space>
            <StatusIcon status={status} />
            <span>WebSocket Test Results</span>
          </Space>
        }
        status={status}
      >
        {websocket.success ? (
          <>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <Statistic
                  title="Connection Time"
                  value={websocket.connectionTime ? Math.round(websocket.connectionTime) : 'N/A'}
                  suffix="ms"
                />
              </Col>
              <Col xs={24} sm={12}>
                <Statistic
                  title="Response Time"
                  value={websocket.responseTime ? Math.round(websocket.responseTime - websocket.connectionTime) : 'N/A'}
                  suffix="ms"
                />
              </Col>
            </Row>
            <Divider />
            <Paragraph>
              <Text strong>Status:</Text> {websocket.message || 'Connection successful'}
            </Paragraph>
          </>
        ) : (
          <Alert
            message="Connection Failed"
            description={websocket.error || 'Unknown error'}
            type="error"
            showIcon
          />
        )}
      </ResultCard>
    );
  };

  // Render API test results
  const renderApiResults = () => {
    if (!results || !results.api) return null;

    const { api } = results;
    const status = api.success ? 'success' : 'error';

    return (
      <ResultCard
        title={
          <Space>
            <StatusIcon status={status} />
            <span>API Test Results</span>
          </Space>
        }
        status={status}
      >
        {api.success ? (
          <>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Response Time"
                  value={Math.round(api.time)}
                  suffix="ms"
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Status Code"
                  value={api.status}
                  suffix={api.statusText}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Response Size"
                  value={api.data ? JSON.stringify(api.data).length : 0}
                  suffix="bytes"
                />
              </Col>
            </Row>
            <Divider />
            <Collapse>
              <Panel header="Response Data" key="1">
                <pre style={{ maxHeight: '200px', overflow: 'auto' }}>
                  {JSON.stringify(api.data, null, 2)}
                </pre>
              </Panel>
            </Collapse>
          </>
        ) : (
          <Alert
            message={`HTTP ${api.status || 'Error'}`}
            description={api.error || 'Unknown error'}
            type="error"
            showIcon
          />
        )}
      </ResultCard>
    );
  };

  // Render performance test results
  const renderPerformanceResults = () => {
    if (!results || !results.performance) return null;

    const { performance } = results;
    const status = performance.success ? 'success' : 'error';

    if (!performance.success) {
      return (
        <ResultCard
          title={
            <Space>
              <StatusIcon status="error" />
              <span>Performance Test Results</span>
            </Space>
          }
          status="error"
        >
          <Alert
            message="Test Failed"
            description={performance.error || 'Unknown error'}
            type="error"
            showIcon
          />
        </ResultCard>
      );
    }

    const { metrics } = performance;

    // Calculate performance score (0-100)
    let performanceScore = 0;
    let scoreComponents = 0;

    if (metrics.pageLoad) {
      // Page load: <1s is great, >3s is poor
      const pageLoadScore = Math.max(0, 100 - (metrics.pageLoad / 30));
      performanceScore += pageLoadScore;
      scoreComponents++;
    }

    if (metrics.frameRate && metrics.frameRate.average) {
      // Frame rate: 60fps is perfect, <30fps is poor
      const frameRateScore = Math.min(100, (metrics.frameRate.average / 60) * 100);
      performanceScore += frameRateScore;
      scoreComponents++;
    }

    if (metrics.memory && metrics.memory.usedJSHeapSize && metrics.memory.jsHeapSizeLimit) {
      // Memory usage: <50% is great, >80% is poor
      const memoryUsagePercent = (metrics.memory.usedJSHeapSize / metrics.memory.jsHeapSizeLimit) * 100;
      const memoryScore = Math.max(0, 100 - memoryUsagePercent);
      performanceScore += memoryScore;
      scoreComponents++;
    }

    // Calculate final score
    const finalScore = scoreComponents > 0 ? Math.round(performanceScore / scoreComponents) : 0;

    // Determine status based on score
    let performanceStatus = 'success';
    if (finalScore < 50) performanceStatus = 'error';
    else if (finalScore < 70) performanceStatus = 'warning';

    return (
      <ResultCard
        title={
          <Space>
            <StatusIcon status={performanceStatus} />
            <span>Performance Test Results</span>
          </Space>
        }
        status={performanceStatus}
      >
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col xs={24} md={8}>
            <MetricCard>
              <Statistic
                title="Performance Score"
                value={finalScore}
                suffix="/100"
                valueStyle={{ color: finalScore >= 70 ? '#52c41a' : finalScore >= 50 ? '#faad14' : '#f5222d' }}
              />
              <Progress
                percent={finalScore}
                status={finalScore >= 70 ? 'success' : finalScore >= 50 ? 'normal' : 'exception'}
                showInfo={false}
                style={{ marginTop: '8px' }}
              />
            </MetricCard>
          </Col>

          {metrics.pageLoad && (
            <Col xs={24} md={8}>
              <MetricCard>
                <Statistic
                  title={
                    <Tooltip title="Time to fully load the page">
                      <span>Page Load Time <InfoCircleOutlined /></span>
                    </Tooltip>
                  }
                  value={Math.round(metrics.pageLoad)}
                  suffix="ms"
                  valueStyle={{ color: metrics.pageLoad < 1000 ? '#52c41a' : metrics.pageLoad < 3000 ? '#faad14' : '#f5222d' }}
                />
              </MetricCard>
            </Col>
          )}

          {metrics.frameRate && metrics.frameRate.average && (
            <Col xs={24} md={8}>
              <MetricCard>
                <Statistic
                  title={
                    <Tooltip title="Average frames per second">
                      <span>Frame Rate <InfoCircleOutlined /></span>
                    </Tooltip>
                  }
                  value={metrics.frameRate.average}
                  suffix="fps"
                  valueStyle={{ color: metrics.frameRate.average >= 50 ? '#52c41a' : metrics.frameRate.average >= 30 ? '#faad14' : '#f5222d' }}
                />
              </MetricCard>
            </Col>
          )}
        </Row>

        <Collapse>
          <Panel header="Detailed Metrics" key="1">
            <Row gutter={[16, 16]}>
              {metrics.domReady && (
                <Col xs={24} md={8}>
                  <Statistic title="DOM Ready Time" value={Math.round(metrics.domReady)} suffix="ms" />
                </Col>
              )}

              {metrics.networkLatency && (
                <Col xs={24} md={8}>
                  <Statistic title="Network Latency" value={Math.round(metrics.networkLatency)} suffix="ms" />
                </Col>
              )}

              {metrics.processingTime && (
                <Col xs={24} md={8}>
                  <Statistic title="Processing Time" value={Math.round(metrics.processingTime)} suffix="ms" />
                </Col>
              )}

              {metrics.backendTime && (
                <Col xs={24} md={8}>
                  <Statistic title="Backend Time" value={Math.round(metrics.backendTime)} suffix="ms" />
                </Col>
              )}

              {metrics.frontendTime && (
                <Col xs={24} md={8}>
                  <Statistic title="Frontend Time" value={Math.round(metrics.frontendTime)} suffix="ms" />
                </Col>
              )}

              {metrics.memory && metrics.memory.usedJSHeapSize && (
                <Col xs={24} md={8}>
                  <Statistic
                    title="Memory Usage"
                    value={(metrics.memory.usedJSHeapSize / (1024 * 1024)).toFixed(1)}
                    suffix="MB"
                  />
                </Col>
              )}

              {metrics.memory && metrics.memory.totalJSHeapSize && (
                <Col xs={24} md={8}>
                  <Statistic
                    title="Total Heap Size"
                    value={(metrics.memory.totalJSHeapSize / (1024 * 1024)).toFixed(1)}
                    suffix="MB"
                  />
                </Col>
              )}

              {metrics.memory && metrics.memory.jsHeapSizeLimit && (
                <Col xs={24} md={8}>
                  <Statistic
                    title="Heap Size Limit"
                    value={(metrics.memory.jsHeapSizeLimit / (1024 * 1024)).toFixed(1)}
                    suffix="MB"
                  />
                </Col>
              )}
            </Row>
          </Panel>
        </Collapse>
      </ResultCard>
    );
  };

  return (
    <TestingContainer>
      <Title level={3}>Testing Tools</Title>
      <Paragraph>
        Use these tools to test various aspects of the application.
      </Paragraph>

      <TestCard title="Test Configuration">
        <InfoBox
          type="info"
          message="Configure Test Parameters"
          description="Set up the parameters for the tests you want to run."
          icon={<InfoCircleOutlined />}
        />

        <FormGroup>
          <div className="label">WebSocket URL:</div>
          <Input
            value={websocketUrl}
            onChange={e => setWebsocketUrl(e.target.value)}
            placeholder="Enter WebSocket URL"
            prefix={<LinkOutlined />}
            disabled={loading}
          />
        </FormGroup>

        <FormGroup>
          <div className="label">API URL:</div>
          <Input
            value={apiUrl}
            onChange={e => setApiUrl(e.target.value)}
            placeholder="Enter API URL"
            prefix={<ApiOutlined />}
            disabled={loading}
          />
        </FormGroup>

        <FormGroup>
          <div className="label">Include Performance Test:</div>
          <Switch
            checked={testPerformance}
            onChange={setTestPerformance}
            disabled={loading}
          />
        </FormGroup>

        <ButtonGroup>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={handleRunAllTests}
            loading={loading}
            disabled={loading}
          >
            Run All Tests
          </Button>

          {enabledTests.includes('component') && (
            <Button
              icon={<CheckCircleOutlined />}
              onClick={runComponentTests}
              disabled={loading || components.length === 0}
            >
              Test Components ({components.length})
            </Button>
          )}

          <Button
            icon={<ApiOutlined />}
            onClick={handleTestWebSocket}
            disabled={loading || !websocketUrl}
          >
            Test WebSocket
          </Button>
          <Button
            icon={<LinkOutlined />}
            onClick={handleTestApi}
            disabled={loading || !apiUrl}
          >
            Test API
          </Button>
          <Button
            icon={<DashboardOutlined />}
            onClick={handleTestPerformance}
            disabled={loading || !testPerformance}
          >
            Test Performance
          </Button>
        </ButtonGroup>
      </TestCard>

      {loading && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>Running tests...</div>
        </div>
      )}

      {componentTestResults && (
        <ResultCard title="Component Test Results" size="small">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="Total Components"
                value={componentTestResults.length}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="Passed"
                value={componentTestResults.filter(r => r.status === 'passed').length}
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="Failed"
                value={componentTestResults.filter(r => r.status === 'failed').length}
                valueStyle={{ color: '#cf1322' }}
                prefix={<CloseCircleOutlined />}
              />
            </Col>
          </Row>

          <Divider />

          <Collapse size="small">
            {componentTestResults.map((result, index) => (
              <Panel
                key={index}
                header={
                  <Space>
                    {result.status === 'passed' ?
                      <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                    }
                    <Text strong>{result.type}</Text>
                    <Text type="secondary">({result.id})</Text>
                  </Space>
                }
              >
                <Text>{result.message}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {new Date(result.timestamp).toLocaleString()}
                </Text>
              </Panel>
            ))}
          </Collapse>
        </ResultCard>
      )}

      {results && (
        <>
          {renderWebSocketResults()}
          {renderApiResults()}
          {renderPerformanceResults()}
        </>
      )}
    </TestingContainer>
  );
};

export default TestingTools;
