/**
 * Workflow Tutorial Manager Component
 * 
 * Orchestrates complete workflow tutorials, managing step progression,
 * validation, and user guidance through complex app creation processes.
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Modal, 
  Button, 
  Steps, 
  Progress, 
  Typography, 
  Space, 
  Card, 
  Alert,
  Tag,
  Tooltip,
  Row,
  Col,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  BulbOutlined,
  RocketOutlined,
  TrophyOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { 
  COMPLETE_WORKFLOW_TUTORIALS,
  validateWorkflowStep,
  trackWorkflowProgress,
  getWorkflowProgress,
  checkWorkflowAchievements
} from './CompleteWorkflowTutorials';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled Components
const WorkflowContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const WorkflowCard = styled(Card)`
  width: 90vw;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
`;

const StepContent = styled.div`
  min-height: 200px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  margin: 16px 0;
`;

const ProgressBar = styled.div`
  position: sticky;
  top: 0;
  background: white;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  z-index: 10;
`;

/**
 * Workflow Tutorial Manager Component
 */
const WorkflowTutorialManager = ({
  isActive = false,
  workflowId = null,
  onComplete,
  onClose,
  onStepChange
}) => {
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState(null);
  const [workflowProgress, setWorkflowProgress] = useState({});
  const [timeSpent, setTimeSpent] = useState(0);
  
  const startTimeRef = useRef(null);
  const timerRef = useRef(null);

  // Load workflow when workflowId changes
  useEffect(() => {
    if (workflowId && COMPLETE_WORKFLOW_TUTORIALS[workflowId]) {
      const workflow = COMPLETE_WORKFLOW_TUTORIALS[workflowId];
      setCurrentWorkflow(workflow);
      
      // Load existing progress
      const progress = getWorkflowProgress(workflowId);
      setWorkflowProgress(progress);
      
      // Set current step based on progress
      if (progress.currentStep) {
        const stepIndex = workflow.steps.findIndex(step => step.id === progress.currentStep);
        setCurrentStepIndex(stepIndex >= 0 ? stepIndex : 0);
      } else {
        setCurrentStepIndex(0);
      }
      
      // Track workflow start
      trackWorkflowProgress(workflowId, workflow.steps[0]?.id, 'started');
    }
  }, [workflowId]);

  // Timer for tracking time spent
  useEffect(() => {
    if (isActive && !isPaused) {
      startTimeRef.current = Date.now();
      timerRef.current = setInterval(() => {
        setTimeSpent(prev => prev + 1);
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive, isPaused]);

  // Get current step
  const currentStep = currentWorkflow?.steps[currentStepIndex];

  // Handle step navigation
  const handleNext = useCallback(async () => {
    if (!currentStep || !currentWorkflow) return;

    // Validate current step if required
    if (currentStep.validation) {
      setIsValidating(true);
      try {
        const result = await validateWorkflowStep(currentStep.id, workflowId);
        setValidationResult(result);
        
        if (!result.success) {
          setIsValidating(false);
          return;
        }
      } catch (error) {
        console.error('Validation error:', error);
        setValidationResult({ success: false, message: 'Validation failed' });
        setIsValidating(false);
        return;
      }
      setIsValidating(false);
    }

    // Track step completion
    trackWorkflowProgress(workflowId, currentStep.id, 'completed');

    // Move to next step or complete workflow
    if (currentStepIndex < currentWorkflow.steps.length - 1) {
      const nextIndex = currentStepIndex + 1;
      setCurrentStepIndex(nextIndex);
      setValidationResult(null);
      
      if (onStepChange) {
        onStepChange(currentWorkflow.steps[nextIndex]);
      }
    } else {
      // Workflow completed
      handleComplete();
    }
  }, [currentStep, currentWorkflow, currentStepIndex, workflowId, onStepChange]);

  // Handle previous step
  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      const prevIndex = currentStepIndex - 1;
      setCurrentStepIndex(prevIndex);
      setValidationResult(null);
      
      if (onStepChange) {
        onStepChange(currentWorkflow.steps[prevIndex]);
      }
    }
  }, [currentStepIndex, currentWorkflow, onStepChange]);

  // Handle workflow completion
  const handleComplete = useCallback(() => {
    if (!workflowId || !currentWorkflow) return;

    // Track completion
    trackWorkflowProgress(workflowId, currentStep?.id, 'finished');
    
    // Check for achievements
    checkWorkflowAchievements(workflowId);
    
    if (onComplete) {
      onComplete(currentWorkflow);
    }
  }, [workflowId, currentWorkflow, currentStep, onComplete]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    setIsPaused(!isPaused);
  }, [isPaused]);

  // Format time
  const formatTime = useCallback((seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Calculate progress percentage
  const progressPercentage = currentWorkflow ? 
    Math.round(((currentStepIndex + 1) / currentWorkflow.steps.length) * 100) : 0;

  if (!isActive || !currentWorkflow || !currentStep) return null;

  return (
    <WorkflowContainer>
      <WorkflowCard
        title={
          <Space>
            <RocketOutlined />
            {currentWorkflow.title}
            <Tag color={currentWorkflow.difficulty === 'easy' ? 'green' : currentWorkflow.difficulty === 'medium' ? 'orange' : 'red'}>
              {currentWorkflow.difficulty}
            </Tag>
            <Tag color="blue">{currentWorkflow.category}</Tag>
          </Space>
        }
        extra={
          <Space>
            <Text type="secondary">
              <ClockCircleOutlined /> {formatTime(timeSpent)}
            </Text>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={onClose}
            />
          </Space>
        }
      >
        {/* Progress Bar */}
        <ProgressBar>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Progress
                percent={progressPercentage}
                size="small"
                strokeColor={{
                  '0%': '#1890ff',
                  '100%': '#52c41a',
                }}
              />
            </Col>
            <Col>
              <Text strong>
                Step {currentStepIndex + 1} of {currentWorkflow.steps.length}
              </Text>
            </Col>
          </Row>
        </ProgressBar>

        {/* Workflow Overview */}
        <div style={{ padding: '0 24px 16px' }}>
          <Paragraph type="secondary">
            {currentWorkflow.description}
          </Paragraph>
          
          <Row gutter={16}>
            <Col span={8}>
              <Text type="secondary">Estimated Time:</Text>
              <br />
              <Text strong>{currentWorkflow.estimatedDuration} minutes</Text>
            </Col>
            <Col span={8}>
              <Text type="secondary">Learning Objectives:</Text>
              <br />
              <Text strong>{currentWorkflow.learningObjectives?.length || 0} skills</Text>
            </Col>
            <Col span={8}>
              <Text type="secondary">Prerequisites:</Text>
              <br />
              <Text strong>
                {currentWorkflow.prerequisites?.length ? 
                  `${currentWorkflow.prerequisites.length} required` : 
                  'None'
                }
              </Text>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* Current Step Content */}
        <div style={{ padding: '0 24px' }}>
          <Title level={3} style={{ marginBottom: 16 }}>
            {currentStep.title}
          </Title>
          
          <StepContent>
            <Paragraph style={{ fontSize: 16, lineHeight: 1.6 }}>
              {currentStep.content}
            </Paragraph>
            
            {/* Step Tips */}
            {currentStep.tips && currentStep.tips.length > 0 && (
              <Alert
                message="💡 Helpful Tips"
                description={
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {currentStep.tips.map((tip, index) => (
                      <li key={index}>{tip}</li>
                    ))}
                  </ul>
                }
                type="info"
                showIcon={false}
                style={{ marginTop: 16 }}
              />
            )}
            
            {/* Validation Result */}
            {validationResult && (
              <Alert
                message={validationResult.message}
                type={validationResult.success ? 'success' : 'error'}
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </StepContent>
        </div>

        {/* Navigation */}
        <div style={{ padding: '16px 24px', borderTop: '1px solid #f0f0f0' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                {currentStepIndex > 0 && (
                  <Button
                    onClick={handlePrevious}
                    disabled={isValidating}
                  >
                    Previous
                  </Button>
                )}
                
                <Button
                  icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                  onClick={handlePauseResume}
                  disabled={isValidating}
                >
                  {isPaused ? 'Resume' : 'Pause'}
                </Button>
              </Space>
            </Col>
            
            <Col>
              <Space>
                <Button onClick={onClose} disabled={isValidating}>
                  Exit Tutorial
                </Button>
                
                <Button
                  type="primary"
                  icon={currentStepIndex === currentWorkflow.steps.length - 1 ? <TrophyOutlined /> : <CheckCircleOutlined />}
                  onClick={handleNext}
                  loading={isValidating}
                >
                  {currentStepIndex === currentWorkflow.steps.length - 1 ? 'Complete Workflow' : 'Next Step'}
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Steps Overview */}
        <div style={{ padding: '16px 24px', background: '#fafafa' }}>
          <Title level={5} style={{ marginBottom: 16 }}>Workflow Steps</Title>
          <Steps
            current={currentStepIndex}
            size="small"
            direction="horizontal"
            style={{ overflowX: 'auto' }}
          >
            {currentWorkflow.steps.map((step, index) => (
              <Step
                key={step.id}
                title={step.title}
                status={
                  index < currentStepIndex ? 'finish' :
                  index === currentStepIndex ? 'process' : 'wait'
                }
              />
            ))}
          </Steps>
        </div>
      </WorkflowCard>
    </WorkflowContainer>
  );
};

export default WorkflowTutorialManager;
