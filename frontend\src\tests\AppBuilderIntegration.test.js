/**
 * App Builder Integration Tests
 * 
 * Tests the complete workflow integration between all App Builder features
 * Verifies component transfer, theme application, and export functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import the components to test
import UnifiedAppBuilder from '../components/unified/UnifiedAppBuilder';
import { DragDropProvider } from '../components/shared/DragDropSystem';
import { ThemeApplicationProvider } from '../components/theme/ThemeApplicationSystem';

// Import reducers
import appBuilderReducer from '../redux/reducers/appBuilderReducer';

// Mock dependencies
jest.mock('antd', () => ({
  Layout: ({ children }) => <div data-testid="layout">{children}</div>,
  Tabs: ({ children, items, onChange, activeKey }) => (
    <div data-testid="tabs">
      {items?.map(item => (
        <div key={item.key} data-testid={`tab-${item.key}`}>
          <button onClick={() => onChange?.(item.key)}>{item.label}</button>
          {activeKey === item.key && item.children}
        </div>
      ))}
    </div>
  ),
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  Typography: {
    Title: ({ children }) => <h1>{children}</h1>,
    Text: ({ children }) => <span>{children}</span>
  },
  Space: ({ children }) => <div>{children}</div>,
  Progress: ({ percent }) => <div data-testid="progress">{percent}%</div>,
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  }
}));

// Mock icons
jest.mock('@ant-design/icons', () => ({
  AppstoreOutlined: () => <span>AppstoreIcon</span>,
  LayoutOutlined: () => <span>LayoutIcon</span>,
  BgColorsOutlined: () => <span>ThemeIcon</span>,
  ExportOutlined: () => <span>ExportIcon</span>,
  PlayCircleOutlined: () => <span>PlayIcon</span>,
  SaveOutlined: () => <span>SaveIcon</span>,
  ShareAltOutlined: () => <span>ShareIcon</span>,
  SettingOutlined: () => <span>SettingIcon</span>,
  CheckCircleOutlined: () => <span>CheckIcon</span>,
  InfoCircleOutlined: () => <span>InfoIcon</span>
}));

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      appBuilder: appBuilderReducer
    },
    preloadedState: {
      appBuilder: {
        components: [],
        layouts: [],
        themes: [],
        activeTheme: null,
        activeFeature: 'components',
        previewMode: false,
        exportSettings: {
          framework: 'react',
          typescript: true,
          includeTests: false,
          includeStyles: true,
          codeQuality: {
            prettier: true,
            eslint: true,
            components: true
          }
        },
        exportHistory: [],
        history: {
          past: [],
          present: null,
          future: []
        },
        loading: false,
        error: null,
        ...initialState
      }
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <ThemeApplicationProvider>
      <DragDropProvider>
        {children}
      </DragDropProvider>
    </ThemeApplicationProvider>
  </Provider>
);

describe('App Builder Integration', () => {
  let store;

  beforeEach(() => {
    store = createTestStore();
    jest.clearAllMocks();
  });

  describe('Unified App Builder', () => {
    test('renders all feature tabs', () => {
      render(
        <TestWrapper store={store}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      expect(screen.getByTestId('tabs')).toBeInTheDocument();
      expect(screen.getByTestId('tab-components')).toBeInTheDocument();
      expect(screen.getByTestId('tab-layouts')).toBeInTheDocument();
      expect(screen.getByTestId('tab-themes')).toBeInTheDocument();
      expect(screen.getByTestId('tab-export')).toBeInTheDocument();
    });

    test('switches between features correctly', async () => {
      render(
        <TestWrapper store={store}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Click on layout tab
      const layoutTab = screen.getByTestId('tab-layouts').querySelector('button');
      fireEvent.click(layoutTab);

      await waitFor(() => {
        expect(store.getState().appBuilder.activeFeature).toBe('layouts');
      });
    });

    test('shows workflow progress correctly', () => {
      const storeWithData = createTestStore({
        components: [{ id: '1', name: 'Test Component', type: 'button' }],
        layouts: [],
        themes: []
      });

      render(
        <TestWrapper store={storeWithData}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Should show progress based on completed steps
      // Components exist, so first step should be completed
      expect(screen.getByText(/Create Components/)).toBeInTheDocument();
    });
  });

  describe('Component to Layout Transfer', () => {
    test('components can be transferred to layouts', async () => {
      const storeWithComponent = createTestStore({
        components: [
          { 
            id: 'comp-1', 
            name: 'Test Button', 
            type: 'button',
            props: { text: 'Click me' }
          }
        ]
      });

      render(
        <TestWrapper store={storeWithComponent}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Switch to layouts tab
      const layoutTab = screen.getByTestId('tab-layouts').querySelector('button');
      fireEvent.click(layoutTab);

      await waitFor(() => {
        expect(store.getState().appBuilder.activeFeature).toBe('layouts');
      });

      // The component should be available for drag and drop
      // This would be tested with more detailed drag-drop simulation
    });
  });

  describe('Theme Application', () => {
    test('themes can be applied globally', async () => {
      const storeWithTheme = createTestStore({
        themes: [
          {
            id: 'theme-1',
            name: 'Test Theme',
            primaryColor: '#ff0000',
            secondaryColor: '#00ff00',
            backgroundColor: '#ffffff',
            textColor: '#000000'
          }
        ]
      });

      render(
        <TestWrapper store={storeWithTheme}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Switch to themes tab
      const themeTab = screen.getByTestId('tab-themes').querySelector('button');
      fireEvent.click(themeTab);

      await waitFor(() => {
        expect(store.getState().appBuilder.activeFeature).toBe('themes');
      });

      // Theme should be available for application
      expect(screen.getByText(/Test Theme/)).toBeInTheDocument();
    });
  });

  describe('Export Functionality', () => {
    test('export is disabled when project is invalid', () => {
      render(
        <TestWrapper store={store}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Switch to export tab
      const exportTab = screen.getByTestId('tab-export').querySelector('button');
      fireEvent.click(exportTab);

      // Export button should be disabled for empty project
      const exportButton = screen.getByText(/Export Application/);
      expect(exportButton).toBeDisabled();
    });

    test('export is enabled when project is valid', async () => {
      const validStore = createTestStore({
        components: [
          { 
            id: 'comp-1', 
            name: 'Valid Component', 
            type: 'button',
            props: { text: 'Valid' }
          }
        ],
        layouts: [
          {
            id: 'layout-1',
            name: 'Valid Layout',
            type: 'grid',
            items: [
              {
                id: 'item-1',
                componentId: 'comp-1',
                position: { x: 0, y: 0, width: 1, height: 1 }
              }
            ]
          }
        ]
      });

      render(
        <TestWrapper store={validStore}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Switch to export tab
      const exportTab = screen.getByTestId('tab-export').querySelector('button');
      fireEvent.click(exportTab);

      await waitFor(() => {
        const exportButton = screen.getByText(/Export Application/);
        expect(exportButton).not.toBeDisabled();
      });
    });
  });

  describe('Complete Workflow', () => {
    test('complete workflow from component creation to export', async () => {
      render(
        <TestWrapper store={store}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Step 1: Create a component
      // This would involve more detailed component creation simulation

      // Step 2: Create a layout and add component
      // This would involve layout creation and drag-drop simulation

      // Step 3: Apply a theme
      // This would involve theme creation and application

      // Step 4: Export the project
      // This would involve export functionality testing

      // For now, just verify the workflow structure exists
      expect(screen.getByTestId('tabs')).toBeInTheDocument();
      expect(screen.getByTestId('tab-components')).toBeInTheDocument();
      expect(screen.getByTestId('tab-layouts')).toBeInTheDocument();
      expect(screen.getByTestId('tab-themes')).toBeInTheDocument();
      expect(screen.getByTestId('tab-export')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles missing dependencies gracefully', () => {
      // Test that the app doesn't crash when components are missing
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      render(
        <TestWrapper store={store}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      expect(screen.getByTestId('layout')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });

    test('shows validation errors appropriately', async () => {
      const storeWithErrors = createTestStore({
        components: [
          { 
            id: 'comp-1', 
            name: '', // Invalid: empty name
            type: 'button'
          }
        ]
      });

      render(
        <TestWrapper store={storeWithErrors}>
          <UnifiedAppBuilder projectId="test-project" />
        </TestWrapper>
      );

      // Switch to export tab to see validation
      const exportTab = screen.getByTestId('tab-export').querySelector('button');
      fireEvent.click(exportTab);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/validation error/i)).toBeInTheDocument();
      });
    });
  });
});

describe('Integration Test Utilities', () => {
  test('test store creates correct initial state', () => {
    const store = createTestStore();
    const state = store.getState();

    expect(state.appBuilder).toBeDefined();
    expect(state.appBuilder.components).toEqual([]);
    expect(state.appBuilder.layouts).toEqual([]);
    expect(state.appBuilder.themes).toEqual([]);
    expect(state.appBuilder.activeFeature).toBe('components');
  });

  test('test wrapper provides all necessary contexts', () => {
    const TestComponent = () => {
      return <div data-testid="test-component">Test</div>;
    };

    render(
      <TestWrapper store={createTestStore()}>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });
});
