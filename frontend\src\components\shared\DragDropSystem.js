/**
 * Enhanced Drag and Drop System
 * 
 * Unified drag-and-drop functionality for App Builder integration
 * Handles component transfer between Component Builder and Layout Designer
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import { 
  setDraggedComponent, 
  clearDraggedComponent, 
  addComponentToLayout 
} from '../../redux/actions/appBuilderActions';
import { getDraggedComponent } from '../../redux/selectors/appBuilderSelectors';

// Drag and Drop Context
const DragDropContext = createContext();

export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error('useDragDrop must be used within a DragDropProvider');
  }
  return context;
};

// Drag and Drop Provider
export const DragDropProvider = ({ children }) => {
  const dispatch = useDispatch();
  const draggedComponent = useSelector(getDraggedComponent);
  const [dragOverTarget, setDragOverTarget] = useState(null);
  const [dropPreview, setDropPreview] = useState(null);

  // Start dragging a component
  const startDrag = useCallback((component, sourceType = 'palette') => {
    const dragData = {
      ...component,
      sourceType,
      dragStartTime: Date.now()
    };
    
    dispatch(setDraggedComponent(dragData));
    
    // Set drag image if available
    if (component.previewImage) {
      const img = new Image();
      img.src = component.previewImage;
      return img;
    }
    
    return null;
  }, [dispatch]);

  // End dragging
  const endDrag = useCallback(() => {
    dispatch(clearDraggedComponent());
    setDragOverTarget(null);
    setDropPreview(null);
  }, [dispatch]);

  // Handle drag over
  const handleDragOver = useCallback((targetId, targetType, position) => {
    setDragOverTarget({ targetId, targetType, position });
    
    // Calculate drop preview
    if (draggedComponent && position) {
      setDropPreview({
        component: draggedComponent,
        position,
        targetId,
        targetType
      });
    }
  }, [draggedComponent]);

  // Handle drop
  const handleDrop = useCallback((targetId, targetType, position) => {
    if (!draggedComponent) {
      message.warning('No component being dragged');
      return false;
    }

    try {
      switch (targetType) {
        case 'layout':
          dispatch(addComponentToLayout(targetId, draggedComponent.id, position));
          message.success(`Component "${draggedComponent.name}" added to layout`);
          break;
          
        case 'canvas':
          // Handle canvas drop (for free-form layouts)
          dispatch(addComponentToLayout(targetId, draggedComponent.id, position));
          message.success(`Component "${draggedComponent.name}" placed on canvas`);
          break;
          
        default:
          message.warning('Invalid drop target');
          return false;
      }
      
      endDrag();
      return true;
    } catch (error) {
      console.error('Drop error:', error);
      message.error('Failed to drop component');
      return false;
    }
  }, [draggedComponent, dispatch, endDrag]);

  const value = {
    draggedComponent,
    dragOverTarget,
    dropPreview,
    startDrag,
    endDrag,
    handleDragOver,
    handleDrop
  };

  return (
    <DragDropContext.Provider value={value}>
      {children}
    </DragDropContext.Provider>
  );
};

// Draggable Component Wrapper
export const DraggableComponent = ({ 
  component, 
  sourceType = 'palette',
  children, 
  className = '',
  style = {},
  onDragStart,
  onDragEnd 
}) => {
  const { startDrag, endDrag } = useDragDrop();

  const handleDragStart = (e) => {
    const dragImage = startDrag(component, sourceType);
    
    // Set drag data for external compatibility
    e.dataTransfer.setData('application/json', JSON.stringify({
      ...component,
      sourceType
    }));
    e.dataTransfer.effectAllowed = 'copy';
    
    // Set custom drag image if available
    if (dragImage) {
      e.dataTransfer.setDragImage(dragImage, 0, 0);
    }
    
    if (onDragStart) {
      onDragStart(e, component);
    }
  };

  const handleDragEnd = (e) => {
    endDrag();
    
    if (onDragEnd) {
      onDragEnd(e, component);
    }
  };

  return (
    <div
      draggable
      className={`draggable-component ${className}`}
      style={{
        cursor: 'grab',
        ...style
      }}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}
    </div>
  );
};

// Drop Zone Component
export const DropZone = ({ 
  targetId, 
  targetType = 'layout',
  onDrop,
  onDragOver,
  onDragLeave,
  children,
  className = '',
  style = {},
  acceptedTypes = ['component'],
  showDropIndicator = true
}) => {
  const { handleDragOver, handleDrop, draggedComponent, dragOverTarget } = useDragDrop();
  const [isOver, setIsOver] = useState(false);

  const handleDragEnter = (e) => {
    e.preventDefault();
    setIsOver(true);
  };

  const handleDragOverInternal = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    
    // Calculate position relative to drop zone
    const rect = e.currentTarget.getBoundingClientRect();
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
      relativeX: (e.clientX - rect.left) / rect.width,
      relativeY: (e.clientY - rect.top) / rect.height
    };
    
    handleDragOver(targetId, targetType, position);
    
    if (onDragOver) {
      onDragOver(e, position);
    }
  };

  const handleDragLeaveInternal = (e) => {
    setIsOver(false);
    
    if (onDragLeave) {
      onDragLeave(e);
    }
  };

  const handleDropInternal = (e) => {
    e.preventDefault();
    setIsOver(false);
    
    // Calculate final drop position
    const rect = e.currentTarget.getBoundingClientRect();
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
      relativeX: (e.clientX - rect.left) / rect.width,
      relativeY: (e.clientY - rect.top) / rect.height,
      gridX: Math.floor((e.clientX - rect.left) / 50), // Assuming 50px grid
      gridY: Math.floor((e.clientY - rect.top) / 50)
    };
    
    const success = handleDrop(targetId, targetType, position);
    
    if (onDrop) {
      onDrop(e, position, success);
    }
  };

  const isValidDrop = draggedComponent && acceptedTypes.includes(draggedComponent.type || 'component');
  const isCurrentTarget = dragOverTarget?.targetId === targetId;

  return (
    <div
      className={`drop-zone ${className} ${isOver ? 'drag-over' : ''} ${isCurrentTarget ? 'drag-target' : ''}`}
      style={{
        position: 'relative',
        minHeight: '100px',
        border: isOver && isValidDrop ? '2px dashed #1890ff' : '2px dashed transparent',
        backgroundColor: isOver && isValidDrop ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
        transition: 'all 0.2s ease',
        ...style
      }}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOverInternal}
      onDragLeave={handleDragLeaveInternal}
      onDrop={handleDropInternal}
    >
      {children}
      
      {/* Drop indicator */}
      {showDropIndicator && isOver && isValidDrop && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(24, 144, 255, 0.2)',
            border: '2px dashed #1890ff',
            borderRadius: '4px',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        >
          <div
            style={{
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            Drop {draggedComponent?.name || 'Component'} Here
          </div>
        </div>
      )}
    </div>
  );
};

// Visual feedback component for drag operations
export const DragPreview = () => {
  const { dropPreview, draggedComponent } = useDragDrop();

  if (!dropPreview || !draggedComponent) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: dropPreview.position?.y || 0,
        left: dropPreview.position?.x || 0,
        pointerEvents: 'none',
        zIndex: 10000,
        opacity: 0.8,
        transform: 'translate(-50%, -50%)',
        padding: '8px',
        backgroundColor: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '4px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      }}
    >
      <div style={{ fontSize: '12px', fontWeight: 'bold' }}>
        {draggedComponent.name}
      </div>
      <div style={{ fontSize: '10px', color: '#666' }}>
        {draggedComponent.type}
      </div>
    </div>
  );
};
