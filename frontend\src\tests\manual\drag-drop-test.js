/**
 * Manual Test Script for Drag and Drop Functionality
 * 
 * This script can be run in the browser console to test drag-and-drop
 * functionality programmatically.
 */

// Test function to simulate drag and drop
function testDragAndDrop() {
  console.log('🧪 Starting Drag and Drop Test...');
  
  // Find component palette
  const palette = document.querySelector('[data-tutorial="component-palette"]');
  if (!palette) {
    console.error('❌ Component palette not found');
    return false;
  }
  console.log('✅ Component palette found');
  
  // Find canvas area
  const canvas = document.querySelector('[data-tutorial="canvas-area"]');
  if (!canvas) {
    console.error('❌ Canvas area not found');
    return false;
  }
  console.log('✅ Canvas area found');
  
  // Find draggable components
  const draggableComponents = palette.querySelectorAll('[draggable="true"]');
  if (draggableComponents.length === 0) {
    console.error('❌ No draggable components found');
    return false;
  }
  console.log(`✅ Found ${draggableComponents.length} draggable components`);
  
  // Test drag data setup
  const firstComponent = draggableComponents[0];
  console.log('🎯 Testing first component:', firstComponent);
  
  // Simulate drag start
  const dragStartEvent = new DragEvent('dragstart', {
    bubbles: true,
    cancelable: true,
    dataTransfer: new DataTransfer()
  });
  
  // Set up drag data (simulate what the component palette does)
  dragStartEvent.dataTransfer.setData('application/json', JSON.stringify({
    type: 'text',
    label: 'Text Component',
    source: 'palette'
  }));
  
  firstComponent.dispatchEvent(dragStartEvent);
  console.log('✅ Drag start event dispatched');
  
  // Simulate drop on canvas
  const dropEvent = new DragEvent('drop', {
    bubbles: true,
    cancelable: true,
    clientX: 200,
    clientY: 150,
    dataTransfer: dragStartEvent.dataTransfer
  });
  
  canvas.dispatchEvent(dropEvent);
  console.log('✅ Drop event dispatched');
  
  // Check if component was added
  setTimeout(() => {
    const components = document.querySelectorAll('[data-component-id]');
    console.log(`📊 Components on canvas: ${components.length}`);
    
    if (components.length > 0) {
      console.log('🎉 Drag and drop test PASSED!');
      return true;
    } else {
      console.log('❌ Drag and drop test FAILED - no components added');
      return false;
    }
  }, 1000);
}

// Test function to check drag and drop setup
function checkDragDropSetup() {
  console.log('🔍 Checking Drag and Drop Setup...');
  
  // Check if DragDropProvider is present
  const dragDropProvider = document.querySelector('[data-testid="drag-drop-provider"]') || 
                          document.querySelector('.drag-drop-provider') ||
                          document.body; // Fallback to body
  
  console.log('📦 DragDropProvider context:', dragDropProvider ? 'Found' : 'Not found');
  
  // Check for drag event listeners
  const elementsWithDragListeners = document.querySelectorAll('[draggable="true"]');
  console.log(`🎯 Draggable elements: ${elementsWithDragListeners.length}`);
  
  // Check for drop zones
  const dropZones = document.querySelectorAll('[data-tutorial="canvas-area"]');
  console.log(`📍 Drop zones: ${dropZones.length}`);
  
  // Check console for any drag-related errors
  console.log('💡 To test manually:');
  console.log('1. Click "Try Enhanced Builder" button');
  console.log('2. Try dragging a component from the left palette');
  console.log('3. Drop it on the canvas area');
  console.log('4. Check browser console for any errors');
  
  return {
    draggableElements: elementsWithDragListeners.length,
    dropZones: dropZones.length,
    hasProvider: !!dragDropProvider
  };
}

// Export functions for manual testing
window.testDragAndDrop = testDragAndDrop;
window.checkDragDropSetup = checkDragDropSetup;

console.log('🚀 Drag and Drop Test Functions Loaded!');
console.log('Run testDragAndDrop() or checkDragDropSetup() in console');
