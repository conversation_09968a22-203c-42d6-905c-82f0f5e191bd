/**
 * Minimal Integrated App Builder for Testing
 * 
 * This is a simplified version to test if the basic structure works
 */

import React, { useState, useCallback } from 'react';
import { Layout, Button, Typography, Card, Alert, Space, message } from 'antd';
import { DragOutlined, DropboxOutlined, PlayCircleOutlined } from '@ant-design/icons';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

// Simple component palette
const DEMO_COMPONENTS = [
  { id: 'text', label: 'Text Component', icon: '📝' },
  { id: 'button', label: 'Button Component', icon: '🔘' },
  { id: 'card', label: 'Card Component', icon: '🃏' },
];

export default function MinimalIntegratedAppBuilder({
  projectId = 'test-project',
  initialComponents = [],
  enableFeatures = {},
  onError
}) {
  const [components, setComponents] = useState(initialComponents);
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // Handle drag start
  const handleDragStart = useCallback((e, component) => {
    setDraggedComponent(component);
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.id,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';
    message.info(`Started dragging ${component.label}`);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setDraggedComponent(null);
  }, []);

  // Handle drag over
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    setIsDragOver(true);
  }, []);

  // Handle drag leave
  const handleDragLeave = useCallback((e) => {
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const dragData = e.dataTransfer.getData('application/json');
      if (dragData) {
        const componentData = JSON.parse(dragData);
        
        // Calculate position
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Add component
        const newComponent = {
          id: `${componentData.type}-${Date.now()}`,
          type: componentData.type,
          label: componentData.label,
          position: { x, y }
        };
        
        setComponents(prev => [...prev, newComponent]);
        message.success(`${componentData.label} added to canvas!`);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
      message.error('Failed to add component');
    }
  }, []);

  // Add component directly
  const handleAddComponent = useCallback((componentType) => {
    const component = DEMO_COMPONENTS.find(c => c.id === componentType);
    if (component) {
      const newComponent = {
        id: `${componentType}-${Date.now()}`,
        type: componentType,
        label: component.label,
        position: { x: Math.random() * 300, y: Math.random() * 200 }
      };
      
      setComponents(prev => [...prev, newComponent]);
      message.success(`${component.label} added!`);
    }
  }, []);

  return (
    <Layout style={{ height: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          🎮 Minimal Integrated App Builder
        </Title>
        <Space>
          <Text type="secondary">Drag & Drop Test Version</Text>
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={() => message.info('This is the working minimal version!')}
          >
            Test Working
          </Button>
        </Space>
      </Header>

      <Layout>
        <Sider width={300} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px' }}>
            <Title level={5}>
              <DragOutlined style={{ marginRight: '8px' }} />
              Component Palette
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
              Drag components to the canvas or click to add
            </Text>
            
            {DEMO_COMPONENTS.map((component) => (
              <Card
                key={component.id}
                size="small"
                hoverable
                draggable
                style={{
                  marginBottom: '8px',
                  cursor: 'grab',
                  border: draggedComponent?.id === component.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
                }}
                onDragStart={(e) => handleDragStart(e, component)}
                onDragEnd={handleDragEnd}
                onClick={() => handleAddComponent(component.id)}
              >
                <Space>
                  <span style={{ fontSize: '20px' }}>{component.icon}</span>
                  <div>
                    <Text strong>{component.label}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      Click or drag to add
                    </Text>
                  </div>
                </Space>
              </Card>
            ))}
          </div>
        </Sider>

        <Content style={{ padding: '24px', background: '#f5f5f5' }}>
          <Card
            title={
              <Space>
                <DropboxOutlined />
                <span>Canvas Area</span>
                <Text type="secondary">({components.length} components)</Text>
              </Space>
            }
            style={{ height: '100%' }}
            bodyStyle={{ 
              position: 'relative', 
              height: 'calc(100% - 57px)',
              background: isDragOver ? '#f6ffed' : '#fafafa',
              border: isDragOver ? '2px dashed #52c41a' : '2px dashed #d9d9d9',
              borderRadius: '8px',
              transition: 'all 0.2s ease'
            }}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {components.length === 0 ? (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                color: '#999'
              }}>
                <DropboxOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <br />
                <Title level={4} type="secondary">
                  Drop components here
                </Title>
                <Text type="secondary">
                  Drag components from the palette or click them to add to canvas
                </Text>
              </div>
            ) : (
              components.map((component) => (
                <div
                  key={component.id}
                  style={{
                    position: 'absolute',
                    left: component.position.x,
                    top: component.position.y,
                    padding: '8px 12px',
                    background: '#fff',
                    border: '2px solid #52c41a',
                    borderRadius: '6px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    cursor: 'move'
                  }}
                >
                  <Space>
                    <span>{DEMO_COMPONENTS.find(c => c.id === component.type)?.icon}</span>
                    <Text strong>{component.label}</Text>
                  </Space>
                </div>
              ))
            )}

            {isDragOver && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                color: '#52c41a',
                fontSize: '18px',
                fontWeight: 'bold'
              }}>
                <DropboxOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <br />
                Release to drop here!
              </div>
            )}
          </Card>
        </Content>
      </Layout>

      {/* Success Alert */}
      {components.length > 0 && (
        <Alert
          message="Drag & Drop Working!"
          description={`Successfully added ${components.length} component(s). The drag-and-drop functionality is working correctly.`}
          type="success"
          showIcon
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            maxWidth: '400px',
            zIndex: 1000
          }}
        />
      )}
    </Layout>
  );
}
