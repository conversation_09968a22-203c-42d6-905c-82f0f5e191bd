/**
 * Theme Application System
 * 
 * Handles real-time theme application to components and layouts
 * Provides preview functionality and theme inheritance
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  applyThemeToComponent, 
  applyThemeToLayout, 
  setActiveTheme 
} from '../../redux/actions/appBuilderActions';
import { 
  getActiveTheme, 
  getThemes, 
  getComponents, 
  getLayouts,
  getComponentsWithThemes,
  getLayoutsWithThemes
} from '../../redux/selectors/appBuilderSelectors';

// Theme Application Context
const ThemeApplicationContext = createContext();

export const useThemeApplication = () => {
  const context = useContext(ThemeApplicationContext);
  if (!context) {
    throw new Error('useThemeApplication must be used within a ThemeApplicationProvider');
  }
  return context;
};

// Theme Application Provider
export const ThemeApplicationProvider = ({ children }) => {
  const dispatch = useDispatch();
  const activeTheme = useSelector(getActiveTheme);
  const themes = useSelector(getThemes);
  const components = useSelector(getComponents);
  const layouts = useSelector(getLayouts);
  const componentsWithThemes = useSelector(getComponentsWithThemes);
  const layoutsWithThemes = useSelector(getLayoutsWithThemes);
  
  const [previewTheme, setPreviewTheme] = useState(null);
  const [themePreviewMode, setThemePreviewMode] = useState(false);

  // Apply theme to a specific component
  const applyThemeToComponentById = (componentId, themeId) => {
    dispatch(applyThemeToComponent(componentId, themeId));
  };

  // Apply theme to a specific layout
  const applyThemeToLayoutById = (layoutId, themeId) => {
    dispatch(applyThemeToLayout(layoutId, themeId));
  };

  // Apply theme to all components
  const applyThemeToAllComponents = (themeId) => {
    components.forEach(component => {
      dispatch(applyThemeToComponent(component.id, themeId));
    });
  };

  // Apply theme to all layouts
  const applyThemeToAllLayouts = (themeId) => {
    layouts.forEach(layout => {
      dispatch(applyThemeToLayout(layout.id, themeId));
    });
  };

  // Apply theme globally (to all components and layouts)
  const applyThemeGlobally = (themeId) => {
    dispatch(setActiveTheme(themeId));
    applyThemeToAllComponents(themeId);
    applyThemeToAllLayouts(themeId);
  };

  // Start theme preview
  const startThemePreview = (themeId) => {
    const theme = themes.find(t => t.id === themeId);
    if (theme) {
      setPreviewTheme(theme);
      setThemePreviewMode(true);
      applyThemeToDOM(theme);
    }
  };

  // End theme preview
  const endThemePreview = () => {
    setPreviewTheme(null);
    setThemePreviewMode(false);
    
    // Restore active theme
    if (activeTheme) {
      applyThemeToDOM(activeTheme);
    } else {
      clearThemeFromDOM();
    }
  };

  // Apply theme to DOM for real-time preview
  const applyThemeToDOM = (theme) => {
    if (!theme) return;

    const root = document.documentElement;
    
    // Apply CSS custom properties
    if (theme.primaryColor) {
      root.style.setProperty('--theme-primary-color', theme.primaryColor);
    }
    if (theme.secondaryColor) {
      root.style.setProperty('--theme-secondary-color', theme.secondaryColor);
    }
    if (theme.backgroundColor) {
      root.style.setProperty('--theme-background-color', theme.backgroundColor);
    }
    if (theme.textColor) {
      root.style.setProperty('--theme-text-color', theme.textColor);
    }
    if (theme.fontFamily) {
      root.style.setProperty('--theme-font-family', theme.fontFamily);
    }
    if (theme.borderRadius) {
      root.style.setProperty('--theme-border-radius', theme.borderRadius);
    }
    if (theme.spacing) {
      root.style.setProperty('--theme-spacing', theme.spacing);
    }

    // Apply additional theme properties
    Object.entries(theme.customProperties || {}).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value);
    });
  };

  // Clear theme from DOM
  const clearThemeFromDOM = () => {
    const root = document.documentElement;
    const themeProperties = [
      '--theme-primary-color',
      '--theme-secondary-color',
      '--theme-background-color',
      '--theme-text-color',
      '--theme-font-family',
      '--theme-border-radius',
      '--theme-spacing'
    ];

    themeProperties.forEach(property => {
      root.style.removeProperty(property);
    });
  };

  // Get effective theme for a component
  const getComponentTheme = (componentId) => {
    const component = componentsWithThemes.find(c => c.id === componentId);
    return component?.theme || previewTheme || activeTheme;
  };

  // Get effective theme for a layout
  const getLayoutTheme = (layoutId) => {
    const layout = layoutsWithThemes.find(l => l.id === layoutId);
    return layout?.theme || previewTheme || activeTheme;
  };

  // Generate CSS for a component with its theme
  const generateComponentCSS = (componentId) => {
    const theme = getComponentTheme(componentId);
    if (!theme) return {};

    return {
      color: theme.textColor,
      backgroundColor: theme.backgroundColor,
      fontFamily: theme.fontFamily,
      borderRadius: theme.borderRadius,
      '--primary-color': theme.primaryColor,
      '--secondary-color': theme.secondaryColor,
      ...theme.customProperties
    };
  };

  // Generate CSS for a layout with its theme
  const generateLayoutCSS = (layoutId) => {
    const theme = getLayoutTheme(layoutId);
    if (!theme) return {};

    return {
      backgroundColor: theme.backgroundColor,
      fontFamily: theme.fontFamily,
      '--primary-color': theme.primaryColor,
      '--secondary-color': theme.secondaryColor,
      '--spacing': theme.spacing,
      '--border-radius': theme.borderRadius,
      ...theme.customProperties
    };
  };

  // Apply active theme to DOM on mount and when it changes
  useEffect(() => {
    if (activeTheme && !themePreviewMode) {
      applyThemeToDOM(activeTheme);
    }
  }, [activeTheme, themePreviewMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearThemeFromDOM();
    };
  }, []);

  const value = {
    // State
    activeTheme,
    themes,
    previewTheme,
    themePreviewMode,
    componentsWithThemes,
    layoutsWithThemes,
    
    // Actions
    applyThemeToComponentById,
    applyThemeToLayoutById,
    applyThemeToAllComponents,
    applyThemeToAllLayouts,
    applyThemeGlobally,
    startThemePreview,
    endThemePreview,
    
    // Utilities
    getComponentTheme,
    getLayoutTheme,
    generateComponentCSS,
    generateLayoutCSS,
    applyThemeToDOM,
    clearThemeFromDOM
  };

  return (
    <ThemeApplicationContext.Provider value={value}>
      {children}
    </ThemeApplicationContext.Provider>
  );
};

// HOC for components that need theme application
export const withThemeApplication = (WrappedComponent) => {
  return React.forwardRef((props, ref) => {
    const themeApplication = useThemeApplication();
    
    return (
      <WrappedComponent
        ref={ref}
        {...props}
        themeApplication={themeApplication}
      />
    );
  });
};

// Hook for components to get their applied theme styles
export const useComponentTheme = (componentId) => {
  const { getComponentTheme, generateComponentCSS } = useThemeApplication();
  
  const theme = getComponentTheme(componentId);
  const styles = generateComponentCSS(componentId);
  
  return { theme, styles };
};

// Hook for layouts to get their applied theme styles
export const useLayoutTheme = (layoutId) => {
  const { getLayoutTheme, generateLayoutCSS } = useThemeApplication();
  
  const theme = getLayoutTheme(layoutId);
  const styles = generateLayoutCSS(layoutId);
  
  return { theme, styles };
};

// Theme Preview Component
export const ThemePreview = ({ themeId, children, style = {} }) => {
  const { themes, startThemePreview, endThemePreview, themePreviewMode } = useThemeApplication();
  const [isHovering, setIsHovering] = useState(false);
  
  const theme = themes.find(t => t.id === themeId);
  
  const handleMouseEnter = () => {
    setIsHovering(true);
    if (!themePreviewMode) {
      startThemePreview(themeId);
    }
  };
  
  const handleMouseLeave = () => {
    setIsHovering(false);
    if (themePreviewMode) {
      endThemePreview();
    }
  };
  
  if (!theme) return children;
  
  return (
    <div
      style={{
        ...style,
        transition: 'all 0.3s ease',
        transform: isHovering ? 'scale(1.02)' : 'scale(1)',
        boxShadow: isHovering ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 4px rgba(0,0,0,0.1)'
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
};

// Real-time Theme Applicator Component
export const RealTimeThemeApplicator = ({ children }) => {
  const { activeTheme, themePreviewMode, previewTheme } = useThemeApplication();
  
  const effectiveTheme = themePreviewMode ? previewTheme : activeTheme;
  
  if (!effectiveTheme) return children;
  
  return (
    <div
      style={{
        color: effectiveTheme.textColor,
        backgroundColor: effectiveTheme.backgroundColor,
        fontFamily: effectiveTheme.fontFamily,
        transition: 'all 0.3s ease'
      }}
    >
      {children}
    </div>
  );
};
