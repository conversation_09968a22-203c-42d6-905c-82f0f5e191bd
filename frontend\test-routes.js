#!/usr/bin/env node

/**
 * Route Testing Script
 * Tests all main application routes to verify accessibility
 */

const http = require('http');
const https = require('https');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TIMEOUT = 10000; // 10 seconds

// Routes to test
const routes = [
  '/',
  '/home',
  '/home-mvp', 
  '/mvp',
  '/app-builder',
  '/app-builder-advanced',
  '/app-builder-enhanced',
  '/templates',
  '/template-system-demo',
  '/websocket',
  '/dashboard',
  '/projects',
  '/profile',
  '/settings'
];

// Test function
function testRoute(route) {
  return new Promise((resolve, reject) => {
    const url = `${BASE_URL}${route}`;
    console.log(`🔍 Testing route: ${route}`);
    
    const request = http.get(url, { timeout: TIMEOUT }, (response) => {
      const { statusCode, statusMessage } = response;
      
      if (statusCode >= 200 && statusCode < 400) {
        console.log(`✅ ${route} - Status: ${statusCode} ${statusMessage}`);
        resolve({ route, status: statusCode, success: true });
      } else {
        console.log(`⚠️  ${route} - Status: ${statusCode} ${statusMessage}`);
        resolve({ route, status: statusCode, success: false, message: statusMessage });
      }
    });

    request.on('error', (error) => {
      console.log(`❌ ${route} - Error: ${error.message}`);
      resolve({ route, success: false, error: error.message });
    });

    request.on('timeout', () => {
      console.log(`⏰ ${route} - Timeout after ${TIMEOUT}ms`);
      request.destroy();
      resolve({ route, success: false, error: 'Timeout' });
    });
  });
}

// Main test function
async function testAllRoutes() {
  console.log('=== App Builder Route Accessibility Test ===\n');
  console.log(`Testing ${routes.length} routes on ${BASE_URL}\n`);

  const results = [];
  
  for (const route of routes) {
    const result = await testRoute(route);
    results.push(result);
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Summary
  console.log('\n=== Test Summary ===');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`✅ Successful: ${successful.length}/${routes.length}`);
  console.log(`❌ Failed: ${failed.length}/${routes.length}`);

  if (failed.length > 0) {
    console.log('\n❌ Failed Routes:');
    failed.forEach(result => {
      console.log(`  - ${result.route}: ${result.error || result.message || 'Unknown error'}`);
    });
  }

  if (successful.length > 0) {
    console.log('\n✅ Successful Routes:');
    successful.forEach(result => {
      console.log(`  - ${result.route}: Status ${result.status}`);
    });
  }

  console.log('\n=== Route Test Complete ===');
  return { successful: successful.length, failed: failed.length, total: routes.length };
}

// Run the test
if (require.main === module) {
  testAllRoutes().catch(console.error);
}

module.exports = { testAllRoutes, testRoute };
