/**
 * Context-Aware Tutorial Trigger System
 * 
 * Automatically detects user context and provides relevant tutorial guidance
 * based on current actions, workflow stage, and user behavior patterns.
 */

import React, { useEffect, useCallback, useRef, useState } from 'react';
import { notification, message } from 'antd';
import { 
  BookOutlined, 
  BulbOutlined, 
  QuestionCircleOutlined,
  RocketOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';

// Context detection patterns
const CONTEXT_PATTERNS = {
  // Component-related contexts
  COMPONENT_HOVER: {
    selector: '[data-tutorial="component-palette"] .component-item',
    event: 'mouseenter',
    delay: 2000,
    tutorialSuggestion: 'getting_started',
    helpText: 'Drag this component to the canvas to add it to your app',
    priority: 'medium'
  },
  
  COMPONENT_DRAG_START: {
    selector: '[data-tutorial="component-palette"] .component-item',
    event: 'dragstart',
    delay: 0,
    tutorialSuggestion: 'getting_started',
    helpText: 'Great! Now drop it on the canvas area',
    priority: 'high'
  },

  CANVAS_EMPTY_HOVER: {
    selector: '[data-tutorial="canvas-area"]',
    event: 'mouseenter',
    condition: () => document.querySelectorAll('[data-tutorial="canvas-area"] .component').length === 0,
    delay: 3000,
    tutorialSuggestion: 'getting_started',
    helpText: 'This is your canvas. Drag components here to start building',
    priority: 'high'
  },

  // Layout-related contexts
  LAYOUT_DESIGNER_FOCUS: {
    selector: '[data-tutorial="layout-designer"]',
    event: 'click',
    delay: 1000,
    tutorialSuggestion: 'complete_workflow',
    helpText: 'Design responsive layouts for your components',
    priority: 'medium'
  },

  // Theme-related contexts
  THEME_MANAGER_INTERACTION: {
    selector: '[data-tutorial="theme-manager"]',
    event: 'click',
    delay: 500,
    tutorialSuggestion: 'complete_workflow',
    helpText: 'Customize your app\'s appearance with themes',
    priority: 'medium'
  },

  // Property editing contexts
  PROPERTY_EDITOR_FOCUS: {
    selector: '[data-tutorial="property-editor"] input, [data-tutorial="property-editor"] select',
    event: 'focus',
    delay: 2000,
    tutorialSuggestion: 'getting_started',
    helpText: 'Customize component properties to match your design',
    priority: 'low'
  },

  // Preview contexts
  PREVIEW_MODE_ACTIVATION: {
    selector: '[data-tutorial="preview-mode"]',
    event: 'click',
    delay: 500,
    tutorialSuggestion: 'getting_started',
    helpText: 'Preview mode shows how your app looks to users',
    priority: 'medium'
  },

  // Advanced feature contexts
  TESTING_TOOLS_HOVER: {
    selector: '[data-tutorial="testing-tools"]',
    event: 'mouseenter',
    delay: 2000,
    tutorialSuggestion: 'advanced_features',
    helpText: 'Test your components for reliability and performance',
    priority: 'low'
  },

  DATA_MANAGEMENT_INTERACTION: {
    selector: '[data-tutorial="data-management"]',
    event: 'click',
    delay: 1000,
    tutorialSuggestion: 'advanced_features',
    helpText: 'Manage data flows and state in your application',
    priority: 'medium'
  },

  // Export contexts
  CODE_EXPORT_HOVER: {
    selector: '[data-tutorial="code-export"]',
    event: 'mouseenter',
    delay: 1500,
    tutorialSuggestion: 'complete_workflow',
    helpText: 'Export your app to multiple frameworks',
    priority: 'medium'
  },

  // Collaboration contexts
  WEBSOCKET_MANAGER_FOCUS: {
    selector: '[data-tutorial="websocket-manager"]',
    event: 'click',
    delay: 1000,
    tutorialSuggestion: 'advanced_features',
    helpText: 'Collaborate with team members in real-time',
    priority: 'low'
  }
};

// User behavior patterns for smart suggestions
const BEHAVIOR_PATTERNS = {
  STRUGGLING_WITH_COMPONENTS: {
    triggers: ['multiple_component_hover', 'no_components_added_5min'],
    suggestion: 'getting_started',
    message: 'Need help getting started? Try our interactive tutorial!'
  },
  
  ADVANCED_USER_PATTERN: {
    triggers: ['multiple_features_used', 'fast_workflow'],
    suggestion: 'advanced_features',
    message: 'Ready for advanced features? Explore collaboration and testing tools!'
  },
  
  DESIGN_FOCUSED_PATTERN: {
    triggers: ['theme_manager_frequent', 'property_editor_heavy_use'],
    suggestion: 'complete_workflow',
    message: 'Perfect your design workflow with our comprehensive tutorial!'
  }
};

/**
 * Context-Aware Tutorial Trigger Component
 */
const ContextAwareTutorialTrigger = ({ 
  onTutorialSuggestion, 
  onContextualHelp,
  isEnabled = true,
  userPreferences = {}
}) => {
  const [activeContexts, setActiveContexts] = useState(new Set());
  const [userBehavior, setUserBehavior] = useState({
    componentHovers: 0,
    featuresUsed: new Set(),
    sessionStartTime: Date.now(),
    lastActivity: Date.now()
  });
  
  const timeoutRefs = useRef(new Map());
  const behaviorTrackingRef = useRef(userBehavior);

  // Update behavior tracking ref
  useEffect(() => {
    behaviorTrackingRef.current = userBehavior;
  }, [userBehavior]);

  // Context detection handler
  const handleContextDetection = useCallback((contextKey, element, event) => {
    if (!isEnabled) return;
    
    const pattern = CONTEXT_PATTERNS[contextKey];
    if (!pattern) return;

    // Check condition if exists
    if (pattern.condition && !pattern.condition()) return;

    // Clear existing timeout for this context
    if (timeoutRefs.current.has(contextKey)) {
      clearTimeout(timeoutRefs.current.get(contextKey));
    }

    // Set new timeout
    const timeoutId = setTimeout(() => {
      // Check if context is still relevant
      if (pattern.condition && !pattern.condition()) return;

      // Update active contexts
      setActiveContexts(prev => new Set([...prev, contextKey]));

      // Show contextual help
      if (onContextualHelp && pattern.helpText) {
        onContextualHelp({
          text: pattern.helpText,
          element,
          priority: pattern.priority,
          tutorialSuggestion: pattern.tutorialSuggestion
        });
      }

      // Track behavior
      setUserBehavior(prev => ({
        ...prev,
        lastActivity: Date.now(),
        featuresUsed: new Set([...prev.featuresUsed, contextKey])
      }));

      // Auto-remove context after showing
      setTimeout(() => {
        setActiveContexts(prev => {
          const newSet = new Set(prev);
          newSet.delete(contextKey);
          return newSet;
        });
      }, 5000);

    }, pattern.delay);

    timeoutRefs.current.set(contextKey, timeoutId);
  }, [isEnabled, onContextualHelp]);

  // Set up event listeners for context detection
  useEffect(() => {
    if (!isEnabled) return;

    const listeners = [];

    Object.entries(CONTEXT_PATTERNS).forEach(([contextKey, pattern]) => {
      const elements = document.querySelectorAll(pattern.selector);
      
      elements.forEach(element => {
        const handler = (event) => handleContextDetection(contextKey, element, event);
        element.addEventListener(pattern.event, handler);
        listeners.push({ element, event: pattern.event, handler });
      });
    });

    // Cleanup function
    return () => {
      listeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      
      // Clear all timeouts
      timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefs.current.clear();
    };
  }, [isEnabled, handleContextDetection]);

  // Behavior pattern analysis
  useEffect(() => {
    const analyzePatterns = () => {
      const behavior = behaviorTrackingRef.current;
      const sessionDuration = Date.now() - behavior.sessionStartTime;
      
      // Check for struggling pattern
      if (behavior.componentHovers > 5 && behavior.featuresUsed.size === 0 && sessionDuration > 300000) { // 5 minutes
        if (onTutorialSuggestion) {
          onTutorialSuggestion({
            type: 'behavior_pattern',
            pattern: 'STRUGGLING_WITH_COMPONENTS',
            suggestion: BEHAVIOR_PATTERNS.STRUGGLING_WITH_COMPONENTS.suggestion,
            message: BEHAVIOR_PATTERNS.STRUGGLING_WITH_COMPONENTS.message
          });
        }
      }
      
      // Check for advanced user pattern
      if (behavior.featuresUsed.size > 5 && sessionDuration < 600000) { // Fast workflow in under 10 minutes
        if (onTutorialSuggestion) {
          onTutorialSuggestion({
            type: 'behavior_pattern',
            pattern: 'ADVANCED_USER_PATTERN',
            suggestion: BEHAVIOR_PATTERNS.ADVANCED_USER_PATTERN.suggestion,
            message: BEHAVIOR_PATTERNS.ADVANCED_USER_PATTERN.message
          });
        }
      }
    };

    const interval = setInterval(analyzePatterns, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [onTutorialSuggestion]);

  // Track component hovers specifically
  useEffect(() => {
    const handleComponentHover = () => {
      setUserBehavior(prev => ({
        ...prev,
        componentHovers: prev.componentHovers + 1,
        lastActivity: Date.now()
      }));
    };

    const componentElements = document.querySelectorAll('[data-tutorial="component-palette"] .component-item');
    componentElements.forEach(element => {
      element.addEventListener('mouseenter', handleComponentHover);
    });

    return () => {
      componentElements.forEach(element => {
        element.removeEventListener('mouseenter', handleComponentHover);
      });
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default ContextAwareTutorialTrigger;

// Export context patterns for use in other components
export { CONTEXT_PATTERNS, BEHAVIOR_PATTERNS };
